# The order of packages is significant, because pip processes them in the order
# of appearance. Changing the order has an impact on the overall integration
# process, which may cause wedges in the gate later.

openstackdocstheme>=2.2.1 # Apache-2.0
reno>=3.1.0 # Apache-2.0
doc8>=0.6.0 # Apache-2.0
sphinx>=2.0.0,!=2.1.0 # BSD
os-api-ref>=1.4.0 # Apache-2.0
ddt>=1.0.1 # MIT
fixtures>=3.0.0 # Apache-2.0/BSD
oslotest>=3.2.0 # Apache-2.0
