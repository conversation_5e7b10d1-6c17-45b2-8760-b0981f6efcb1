{"info": {"_postman_id": "502de428-6566-40d0-b11c-ccb660ac6cdc", "name": "健康检查巡检系统API Copy", "description": "健康检查巡检系统相关接口", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json"}, "item": [{"name": "健康检查巡检", "item": [{"name": "创建健康巡检", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": "{{url}}/api/v1/security/create-health-check", "description": "创建新的巡检任务"}, "response": []}, {"name": "执行健康巡检-1-CLOUD_DB_STATUS", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/check-health?history_id=1&category_id=18&category_code=CLOUD_DB_STATUS", "host": ["{{url}}"], "path": ["api", "v1", "security", "check-health"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}, {"key": "category_id", "value": "18", "description": "巡检类别ID"}, {"key": "category_code", "value": "CLOUD_DB_STATUS", "description": "巡检类别编码"}]}, "description": "执行指定类别的健康巡检"}, "response": []}, {"name": "执行健康巡检-1-CLOUD_NOVA", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/check-health?history_id=1&category_id=19&category_code=CLOUD_NOVA", "host": ["{{url}}"], "path": ["api", "v1", "security", "check-health"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}, {"key": "category_id", "value": "19", "description": "巡检类别ID"}, {"key": "category_code", "value": "CLOUD_NOVA", "description": "巡检类别编码"}]}, "description": "执行指定类别的健康巡检"}, "response": []}, {"name": "执行健康巡检-1-CLOUD_KEYSTONE", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/check-health?history_id=1&category_id=40&category_code=CLOUD_KEYSTONE", "host": ["{{url}}"], "path": ["api", "v1", "security", "check-health"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}, {"key": "category_id", "value": "40", "description": "巡检类别ID"}, {"key": "category_code", "value": "CLOUD_KEYSTONE", "description": "巡检类别编码"}]}, "description": "执行指定类别的健康巡检"}, "response": []}, {"name": "执行健康巡检-1-CLOUD_CINDER", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/check-health?history_id=1&category_id=20&category_code=CLOUD_CINDER", "host": ["{{url}}"], "path": ["api", "v1", "security", "check-health"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}, {"key": "category_id", "value": "20", "description": "巡检类别ID"}, {"key": "category_code", "value": "CLOUD_CINDER", "description": "巡检类别编码"}]}, "description": "执行指定类别的健康巡检"}, "response": []}, {"name": "执行健康巡检-1-CLOUD_NEUTRON_SERVER", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/check-health?history_id=1&category_id=41&category_code=CLOUD_NEUTRON_SERVER", "host": ["{{url}}"], "path": ["api", "v1", "security", "check-health"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}, {"key": "category_id", "value": "41", "description": "巡检类别ID"}, {"key": "category_code", "value": "CLOUD_NEUTRON_SERVER", "description": "巡检类别编码"}]}, "description": "执行指定类别的健康巡检"}, "response": []}, {"name": "执行健康巡检-1-CLOUD_GLANCE", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/check-health?history_id=1&category_id=42&category_code=CLOUD_GLANCE", "host": ["{{url}}"], "path": ["api", "v1", "security", "check-health"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}, {"key": "category_id", "value": "42", "description": "巡检类别ID"}, {"key": "category_code", "value": "CLOUD_GLANCE", "description": "巡检类别编码"}]}, "description": "执行指定类别的健康巡检"}, "response": []}, {"name": "执行健康巡检-1-CLOUD_NEUTRON_DHCP_AGENT", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/check-health?history_id=1&category_id=44&category_code=CLOUD_NEUTRON_DHCP_AGENT", "host": ["{{url}}"], "path": ["api", "v1", "security", "check-health"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}, {"key": "category_id", "value": "44", "description": "巡检类别ID"}, {"key": "category_code", "value": "CLOUD_NEUTRON_DHCP_AGENT", "description": "巡检类别编码"}]}, "description": "执行指定类别的健康巡检"}, "response": []}, {"name": "执行健康巡检-1-CLOUD_KEEPALVED", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/check-health?history_id=1&category_id=45&category_code=CLOUD_KEEPALVED", "host": ["{{url}}"], "path": ["api", "v1", "security", "check-health"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}, {"key": "category_id", "value": "45", "description": "巡检类别ID"}, {"key": "category_code", "value": "CLOUD_KEEPALVED", "description": "巡检类别编码"}]}, "description": "执行指定类别的健康巡检"}, "response": []}, {"name": "执行健康巡检-1-CLOUD_NEUTRON_OPENVSWITCH_AGENT", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/check-health?history_id=1&category_id=46&category_code=CLOUD_NEUTRON_OPENVSWITCH_AGENT", "host": ["{{url}}"], "path": ["api", "v1", "security", "check-health"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}, {"key": "category_id", "value": "46", "description": "巡检类别ID"}, {"key": "category_code", "value": "CLOUD_NEUTRON_OPENVSWITCH_AGENT", "description": "巡检类别编码"}]}, "description": "执行指定类别的健康巡检"}, "response": []}, {"name": "执行健康巡检-1-CLOUD_NOVA_COMPUTE", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/check-health?history_id=1&category_id=47&category_code=CLOUD_NOVA_COMPUTE", "host": ["{{url}}"], "path": ["api", "v1", "security", "check-health"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}, {"key": "category_id", "value": "47", "description": "巡检类别ID"}, {"key": "category_code", "value": "CLOUD_NOVA_COMPUTE", "description": "巡检类别编码"}]}, "description": "执行指定类别的健康巡检"}, "response": []}, {"name": "执行健康巡检-1-CLOUD_CINDER_VOLUME", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/check-health?history_id=1&category_id=48&category_code=CLOUD_CINDER_VOLUME", "host": ["{{url}}"], "path": ["api", "v1", "security", "check-health"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}, {"key": "category_id", "value": "48", "description": "巡检类别ID"}, {"key": "category_code", "value": "CLOUD_CINDER_VOLUME", "description": "巡检类别编码"}]}, "description": "执行指定类别的健康巡检"}, "response": []}, {"name": "执行健康巡检-1-CLOUD_NEUTRON", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/check-health?history_id=1&category_id=43&category_code=CLOUD_NEUTRON", "host": ["{{url}}"], "path": ["api", "v1", "security", "check-health"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}, {"key": "category_id", "value": "43", "description": "巡检类别ID"}, {"key": "category_code", "value": "CLOUD_NEUTRON", "description": "巡检类别编码"}]}, "description": "执行指定类别的健康巡检"}, "response": []}, {"name": "执行健康巡检-1-CEPH_STATUS", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/check-health?history_id=1&category_id=26&category_code=CEPH_STATUS", "host": ["{{url}}"], "path": ["api", "v1", "security", "check-health"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}, {"key": "category_id", "value": "26", "description": "巡检类别ID"}, {"key": "category_code", "value": "CEPH_STATUS", "description": "巡检类别编码"}]}, "description": "执行指定类别的健康巡检"}, "response": []}, {"name": "执行健康巡检-1-CEPH_POOL", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/check-health?history_id=1&category_id=28&category_code=CEPH_POOL", "host": ["{{url}}"], "path": ["api", "v1", "security", "check-health"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}, {"key": "category_id", "value": "28", "description": "巡检类别ID"}, {"key": "category_code", "value": "CEPH_POOL", "description": "巡检类别编码"}]}, "description": "执行指定类别的健康巡检"}, "response": []}, {"name": "执行健康巡检-1-CEPH_OBJECT", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/check-health?history_id=1&category_id=29&category_code=CEPH_OBJECT", "host": ["{{url}}"], "path": ["api", "v1", "security", "check-health"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}, {"key": "category_id", "value": "29", "description": "巡检类别ID"}, {"key": "category_code", "value": "CEPH_OBJECT", "description": "巡检类别编码"}]}, "description": "执行指定类别的健康巡检"}, "response": []}, {"name": "执行健康巡检-1-CEPH_PG", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/check-health?history_id=1&category_id=30&category_code=CEPH_PG", "host": ["{{url}}"], "path": ["api", "v1", "security", "check-health"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}, {"key": "category_id", "value": "30", "description": "巡检类别ID"}, {"key": "category_code", "value": "CEPH_PG", "description": "巡检类别编码"}]}, "description": "执行指定类别的健康巡检"}, "response": []}, {"name": "执行健康巡检-1-CEPH_OSD", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/check-health?history_id=1&category_id=31&category_code=CEPH_OSD", "host": ["{{url}}"], "path": ["api", "v1", "security", "check-health"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}, {"key": "category_id", "value": "31", "description": "巡检类别ID"}, {"key": "category_code", "value": "CEPH_OSD", "description": "巡检类别编码"}]}, "description": "执行指定类别的健康巡检"}, "response": []}, {"name": "执行健康巡检-1-check_network_route", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/check-health?history_id=1&category_id=33&category_code=check_network_route", "host": ["{{url}}"], "path": ["api", "v1", "security", "check-health"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}, {"key": "category_id", "value": "33", "description": "巡检类别ID"}, {"key": "category_code", "value": "check_network_route", "description": "巡检类别编码"}]}, "description": "执行指定类别的健康巡检"}, "response": []}, {"name": "执行健康巡检-1-check_network_gateway", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/check-health?history_id=1&category_id=34&category_code=check_network_gateway", "host": ["{{url}}"], "path": ["api", "v1", "security", "check-health"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}, {"key": "category_id", "value": "34", "description": "巡检类别ID"}, {"key": "category_code", "value": "check_network_gateway", "description": "巡检类别编码"}]}, "description": "执行指定类别的健康巡检"}, "response": []}, {"name": "执行健康巡检-1-NETWORK_PORT", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/check-health?history_id=1&category_id=36&category_code=NETWORK_PORT", "host": ["{{url}}"], "path": ["api", "v1", "security", "check-health"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}, {"key": "category_id", "value": "36", "description": "巡检类别ID"}, {"key": "category_code", "value": "NETWORK_PORT", "description": "巡检类别编码"}]}, "description": "执行指定类别的健康巡检"}, "response": []}, {"name": "执行健康巡检-1-HOSTOS_DISK_USED_PERCENT", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/check-health?history_id=1&category_id=7&category_code=HOSTOS_DISK_USED_PERCENT", "host": ["{{url}}"], "path": ["api", "v1", "security", "check-health"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}, {"key": "category_id", "value": "7", "description": "巡检类别ID"}, {"key": "category_code", "value": "HOSTOS_DISK_USED_PERCENT", "description": "巡检类别编码"}]}, "description": "执行指定类别的健康巡检"}, "response": []}, {"name": "执行健康巡检-1-HOSTOS_CPU_USED_PERCENT", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/check-health?history_id=1&category_id=10&category_code=HOSTOS_CPU_USED_PERCENT", "host": ["{{url}}"], "path": ["api", "v1", "security", "check-health"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}, {"key": "category_id", "value": "10", "description": "巡检类别ID"}, {"key": "category_code", "value": "HOSTOS_CPU_USED_PERCENT", "description": "巡检类别编码"}]}, "description": "执行指定类别的健康巡检"}, "response": []}, {"name": "执行健康巡检-1-HOSTOS_MEMORY_USED_PERCENT", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/check-health?history_id=1&category_id=11&category_code=HOSTOS_MEMORY_USED_PERCENT", "host": ["{{url}}"], "path": ["api", "v1", "security", "check-health"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}, {"key": "category_id", "value": "11", "description": "巡检类别ID"}, {"key": "category_code", "value": "HOSTOS_MEMORY_USED_PERCENT", "description": "巡检类别编码"}]}, "description": "执行指定类别的健康巡检"}, "response": []}, {"name": "执行健康巡检-1-HOSTOS_WARN_EVENT", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/check-health?history_id=1&category_id=12&category_code=HOSTOS_WARN_EVENT", "host": ["{{url}}"], "path": ["api", "v1", "security", "check-health"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}, {"key": "category_id", "value": "12", "description": "巡检类别ID"}, {"key": "category_code", "value": "HOSTOS_WARN_EVENT", "description": "巡检类别编码"}]}, "description": "执行指定类别的健康巡检"}, "response": []}, {"name": "执行健康巡检-1-HOSTOS_NETWORK_STATUS", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/check-health?history_id=1&category_id=13&category_code=HOSTOS_NETWORK_STATUS", "host": ["{{url}}"], "path": ["api", "v1", "security", "check-health"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}, {"key": "category_id", "value": "13", "description": "巡检类别ID"}, {"key": "category_code", "value": "HOSTOS_NETWORK_STATUS", "description": "巡检类别编码"}]}, "description": "执行指定类别的健康巡检"}, "response": []}, {"name": "执行健康巡检-1-CLOUD_HA_STATUS", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/check-health?history_id=1&category_id=21&category_code=CLOUD_HA_STATUS", "host": ["{{url}}"], "path": ["api", "v1", "security", "check-health"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}, {"key": "category_id", "value": "21", "description": "巡检类别ID"}, {"key": "category_code", "value": "CLOUD_HA_STATUS", "description": "巡检类别编码"}]}, "description": "执行指定类别的健康巡检"}, "response": []}, {"name": "执行健康巡检-1-GUESTOS_VOLUME", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/check-health?history_id=1&category_id=22&category_code=GUESTOS_VOLUME", "host": ["{{url}}"], "path": ["api", "v1", "security", "check-health"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}, {"key": "category_id", "value": "22", "description": "巡检类别ID"}, {"key": "category_code", "value": "GUESTOS_VOLUME", "description": "巡检类别编码"}]}, "description": "执行指定类别的健康巡检"}, "response": []}, {"name": "执行健康巡检-1-GUESTOS_CPU_USED_PERCENT", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/check-health?history_id=1&category_id=24&category_code=GUESTOS_CPU_USED_PERCENT", "host": ["{{url}}"], "path": ["api", "v1", "security", "check-health"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}, {"key": "category_id", "value": "24", "description": "巡检类别ID"}, {"key": "category_code", "value": "GUESTOS_CPU_USED_PERCENT", "description": "巡检类别编码"}]}, "description": "执行指定类别的健康巡检"}, "response": []}, {"name": "执行健康巡检-1-GUESTOS_MEMORY_USED_PERCENT", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/check-health?history_id=1&category_id=23&category_code=GUESTOS_MEMORY_USED_PERCENT", "host": ["{{url}}"], "path": ["api", "v1", "security", "check-health"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}, {"key": "category_id", "value": "23", "description": "巡检类别ID"}, {"key": "category_code", "value": "GUESTOS_MEMORY_USED_PERCENT", "description": "巡检类别编码"}]}, "description": "执行指定类别的健康巡检"}, "response": []}, {"name": "更新健康巡检状态", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/update-health-check-state?id=1&state=1", "host": ["{{url}}"], "path": ["api", "v1", "security", "update-health-check-state"], "query": [{"key": "id", "value": "1", "description": "巡检历史ID"}, {"key": "state", "value": "1", "description": "状态：0-进行中，1-已完成，2-失败"}]}, "description": "更新健康巡检状态"}, "response": []}, {"name": "巡检记录列表查询", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/health-check-history-list?pageIndex=1&pageSize=10", "host": ["{{url}}"], "path": ["api", "v1", "security", "health-check-history-list"], "query": [{"key": "pageIndex", "value": "1"}, {"key": "pageSize", "value": "10"}]}, "description": "获取巡检记录列表，支持分页"}, "response": []}, {"name": "巡检结果集合查询", "protocolProfileBehavior": {"disableCookies": false, "strictSSL": false}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json, text/plain, */*", "type": "default"}, {"key": "X-Auth-Token", "value": "{{token}}", "type": "default"}], "url": {"raw": "{{url}}/api/v1/security/health-check-result?history_id=1", "host": ["{{url}}"], "path": ["api", "v1", "security", "health-check-result"], "query": [{"key": "history_id", "value": "1", "description": "巡检历史ID"}]}, "description": "获取指定巡检历史ID的巡检结果集合"}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "url", "value": "http://localhost:8080", "type": "string"}, {"key": "token", "value": "your_auth_token_here", "type": "string"}]}