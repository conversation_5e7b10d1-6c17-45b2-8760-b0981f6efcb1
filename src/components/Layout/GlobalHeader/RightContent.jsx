// Copyright 2021 99cloud
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { Button, Col, Row, message as $message } from 'antd';
import client from 'client';
import OverviewStore from 'stores/overview-admin';
import Avatar from './AvatarDropdown';
import styles from './index.less';

export class GlobalHeaderRight extends Component {
  constructor(props) {
    super(props);
    this.state = {
      uccpsUrl: '',
      visUrl: '',
      showUccpsButton: true,
      showLargeScreenButton: true,
      showHealthInspectionButton: true,
      showNewHealthInspectionButton: true,
    };
    this.overviewStore = new OverviewStore();
  }

  componentDidMount() {
    client.ustack.getUccpsUrl().then((result) => {
      this.setState({ uccpsUrl: result.url });
    });
    client.ustack.getVisUrl().then((result) => {
      this.setState({ visUrl: result.url });
      this.overviewStore.setVisUrl(result.url);
    });
    this.getOtherSettings();
  }

  async getOtherSettings() {
    try {
      const settings = await client.ustack.getOtherSettings();
      this.setState({
        showUccpsButton: settings.show_uccps_button,
        showLargeScreenButton: settings.show_large_screen_button,
        showHealthInspectionButton: settings.show_health_inspection_button,
        showNewHealthInspectionButton: settings.show_new_health_inspection_button !== false,
      });
    } catch (error) {
      console.error('获取其他设置失败:', error);
      // 异常情况下使用默认值
      this.setState({
        showUccpsButton: true,
        showLargeScreenButton: true,
        showHealthInspectionButton: true,
        showNewHealthInspectionButton: true,
      });
    }
  }

  get isAdminPage() {
    const { isAdminPage = false } = this.props;
    return isAdminPage;
  }

  get isUserCenterPage() {
    const { isUserCenterPage = false } = this.props;
    return isUserCenterPage;
  }

  gotoVis = () => {
    if (this.state.visUrl) {
      window.open(this.state.visUrl, '_blanck');
    } else {
      $message.error(
        t(
          'The current platform has not yet enabled the {name} management module. Please contact the administrator to enable it',
          { name: t('Large Screen') }
        )
      );
    }
  };

  gotoUccps = () => {
    if (this.state.uccpsUrl) {
      window.open(this.state.uccpsUrl, '_blanck');
    } else {
      $message.error(
        t(
          'The current platform has not yet enabled the {name} management module. Please contact the administrator to enable it',
          { name: t('Cloud Container Engine') }
        )
      );
    }
  };

  renderConsole() {
    if (this.isAdminPage || this.isUserCenterPage) {
      return (
        <Button
          type="link"
          href="/base/overview"
          className={styles['single-link']}
        >
          {t('Project Console')}
        </Button>
      );
    }
    return null;
  }

  renderAdministrator() {
    const { rootStore: { hasAdminPageRole = false } = {} } = this.props;
    if (!hasAdminPageRole || this.isAdminPage) {
      return null;
    }
    return (
      <Button
        type="link"
        href="/base/overview-admin"
        className={styles['single-link']}
      >
        {t('Administrator')}
      </Button>
    );
  }

  renderExtra() {
    return null;
  }

  renderExtraLink() {
    const {
      showUccpsButton,
      showLargeScreenButton,
      showHealthInspectionButton,
      showNewHealthInspectionButton,
    } = this.state;
    return (
      <>
        {this.isAdminPage ? (
          <>
            <Button
              type="link"
              href="/base/health-inspection-admin"
              className={styles['single-link']}
              hidden={!showHealthInspectionButton}
            >
              {t('Health Inspection')}
            </Button>
            <Button
              type="link"
              href="/base/new-health-inspection-admin"
              className={styles['single-link']}
              hidden={!showNewHealthInspectionButton}
            >
              {t('New Health Inspection')}
            </Button>
            <Button
              type="link"
              className={styles['single-link']}
              onClick={this.gotoVis}
              hidden={!showLargeScreenButton}
            >
              {t('Large Screen')}
            </Button>
          </>
        ) : null}
        <Button
          type="link"
          className={styles['single-link']}
          onClick={this.gotoUccps}
          hidden={!showUccpsButton}
        >
          {t('Uccps')}
        </Button>
      </>
    );
  }

  render() {
    return (
      <div className={styles.right}>
        <Row justify="space-between" align="middle" gutter={10}>
          <Col>
            {this.renderExtraLink()}
            {this.renderConsole()}
            {this.renderAdministrator()}
          </Col>
          {this.renderExtra()}
          <Col>
            <Avatar menu />
          </Col>
        </Row>
      </div>
    );
  }
}

export default inject('rootStore')(observer(GlobalHeaderRight));
