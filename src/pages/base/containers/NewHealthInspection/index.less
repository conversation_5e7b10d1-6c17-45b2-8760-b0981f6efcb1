.container {
  padding: 24px;
  background: #f5f5f5;
  height: calc(100vh - 64px);
  overflow-y: auto;
  font-size: 14px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif;
}

.header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.titleSection {
  margin-bottom: 20px;
}

.title {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
}

.description {
  font-size: 14px;
  color: #8c8c8c;
  margin: 0;
  line-height: 1.5;
}

.actionButtons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.leftButtons {
  flex: 0 0 auto;
}

.rightButtons {
  flex: 0 0 auto;
}

.content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.inspectionConfig {
  padding: 0;
}

.inspectionCategory {
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.categoryTitle {
  padding: 20px 24px;
  background: #fafafa;
  font-weight: bold;
  font-size: 14px;
  color: #262626;
  border-bottom: 1px solid #f0f0f0;
}

.categoryContent {
  padding: 24px;
  background: white;
}

.inspectionItemWrapper {
  width: 100%;
  height: 100%;
}

.inspectionItem {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
  transition: all 0.3s ease;
  width: 100%;
  margin-bottom: 0;
  min-height: 48px;

  &:hover {
    border-color: #1890ff;
    background: #f6ffed;
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
  }

  :global(.ant-checkbox-wrapper) {
    font-size: 14px;
    color: #000000;
    margin: 0;
    width: 100%;
    line-height: 1.4;
    font-weight: normal;

    &:hover {
      color: #000000;
    }
  }

  :global(.ant-checkbox-checked) {
    + span {
      color: #000000;
      font-weight: normal;
    }
  }

  &.checked {
    background: #f6ffed;
    border-color: #52c41a;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .inspectionItemWrapper {
    margin-bottom: 12px;
  }
}

@media (max-width: 768px) {
  .actionButtons {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .leftButtons,
  .rightButtons {
    width: 100%;
    text-align: center;
  }

  .inspectionItemWrapper {
    margin-bottom: 8px;
  }

  .inspectionItem {
    padding: 10px 12px;
    min-height: 44px;
  }
}

// 巡检进度样式
.progressContainer {
  padding: 20px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  border-left: 4px solid #1890ff;
}

.progressText {
  font-size: 16px;
  color: #1890ff;
  margin-bottom: 12px;
  font-weight: 500;
}

// 动画效果
.inspectionItem {
  :global(.ant-checkbox-wrapper) {
    transition: all 0.3s ease;
  }
}

.collapse {
  :global(.ant-collapse-item) {
    transition: all 0.3s ease;
  }
}

// 选中状态的特殊样式
.inspectionItem {
  :global(.ant-checkbox-wrapper-checked) {
    .inspectionItem {
      background: #f6ffed;
      border-color: #52c41a;
    }
  }
}

// 禁用状态样式
.inspectionItem {
  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    
    :global(.ant-checkbox-wrapper) {
      cursor: not-allowed;
    }
  }
}
