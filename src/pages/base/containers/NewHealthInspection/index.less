.container {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.titleSection {
  flex: 1;
}

.title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
}

.description {
  font-size: 14px;
  color: #8c8c8c;
  margin: 0;
  line-height: 1.5;
}

.actionButtons {
  flex-shrink: 0;
  margin-left: 24px;
}

.content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.inspectionConfig {
  padding: 0;
}

.collapse {
  border: none;
  background: transparent;

  :global(.ant-collapse-item) {
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }
  }

  :global(.ant-collapse-header) {
    padding: 20px 24px;
    background: #fafafa;
    border: none;
    font-weight: 500;
    font-size: 16px;

    &:hover {
      background: #f0f0f0;
    }
  }

  :global(.ant-collapse-content) {
    border: none;
    background: white;
  }

  :global(.ant-collapse-content-box) {
    padding: 24px;
  }
}

.panelHeader {
  display: flex;
  align-items: center;
  width: 100%;

  :global(.ant-checkbox-wrapper) {
    font-size: 16px;
    font-weight: 500;
    color: #262626;
  }
}

.inspectionPanel {
  :global(.ant-collapse-header) {
    .panelHeader {
      :global(.ant-checkbox-wrapper) {
        &:hover {
          color: #1890ff;
        }
      }
    }
  }
}

.inspectionItemWrapper {
  width: 100%;
  height: 100%;
}

.inspectionItem {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
  transition: all 0.3s ease;
  width: 100%;
  margin-bottom: 0;
  min-height: 48px;

  &:hover {
    border-color: #1890ff;
    background: #f6ffed;
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
  }

  :global(.ant-checkbox-wrapper) {
    font-size: 14px;
    color: #595959;
    margin: 0;
    width: 100%;
    line-height: 1.4;

    &:hover {
      color: #1890ff;
    }
  }

  :global(.ant-checkbox-checked) {
    + span {
      color: #1890ff;
      font-weight: 500;
    }
  }

  &.checked {
    background: #f6ffed;
    border-color: #52c41a;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .inspectionItemWrapper {
    margin-bottom: 12px;
  }
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: stretch;
  }

  .actionButtons {
    margin-left: 0;
    margin-top: 16px;
  }

  .inspectionItemWrapper {
    margin-bottom: 8px;
  }

  .inspectionItem {
    padding: 10px 12px;
    min-height: 44px;
  }
}

// 巡检进度样式
.progressContainer {
  padding: 20px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  border-left: 4px solid #1890ff;
}

.progressText {
  font-size: 16px;
  color: #1890ff;
  margin-bottom: 12px;
  font-weight: 500;
}

// 动画效果
.inspectionItem {
  :global(.ant-checkbox-wrapper) {
    transition: all 0.3s ease;
  }
}

.collapse {
  :global(.ant-collapse-item) {
    transition: all 0.3s ease;
  }
}

// 选中状态的特殊样式
.inspectionItem {
  :global(.ant-checkbox-wrapper-checked) {
    .inspectionItem {
      background: #f6ffed;
      border-color: #52c41a;
    }
  }
}

// 禁用状态样式
.inspectionItem {
  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    
    :global(.ant-checkbox-wrapper) {
      cursor: not-allowed;
    }
  }
}
