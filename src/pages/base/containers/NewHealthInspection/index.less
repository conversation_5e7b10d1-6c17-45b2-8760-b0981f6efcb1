.container {
  padding: 24px;
  background: #f5f5f5;
  height: calc(100vh - 64px);
  display: flex;
  flex-direction: column;
  font-size: 14px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif;
}

.header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.titleSection {
  margin-bottom: 20px;
}

.title {
  margin: 0;
  line-height: 1.5;

  .titleMain {
    font-size: 20px;
    font-weight: 600;
    color: #262626;
  }

  .titleDescription {
    font-size: 14px;
    font-weight: normal;
    color: #8c8c8c;
    margin-left: 8px;
  }
}

.actionButtons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.leftButtons {
  flex: 0 0 auto;
}

.rightButtons {
  flex: 0 0 auto;
}

.content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.inspectionConfig {
  padding: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.globalControls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 0;
  flex-shrink: 0;
}

.globalTitle {
  font-size: 16px;
  font-weight: bold;
  color: #262626;
}

.globalButtons {
  // 按钮样式
}

.collapse {
  border: none;
  background: transparent;
  flex: 1;
  overflow-y: auto;

  :global(.ant-collapse-item) {
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }
  }

  :global(.ant-collapse-header) {
    padding: 20px 24px;
    background: #fafafa;
    border: none;
    font-weight: bold;
    font-size: 14px;

    &:hover {
      background: #f0f0f0;
    }
  }

  :global(.ant-collapse-content) {
    border: none;
    background: white;
  }

  :global(.ant-collapse-content-box) {
    padding: 24px;
  }
}

.panelHeader {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 8px;


}

.categoryTitleText {
  font-size: 14px;
  font-weight: bold;
  color: #262626;
  user-select: none;
}

.inspectionItemWrapper {
  width: 100%;
  height: 100%;
}

.inspectionItem {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
  transition: all 0.3s ease;
  width: 100%;
  margin-bottom: 0;
  min-height: 48px;
  gap: 12px;

  &:hover {
    border-color: #1890ff;
    background: #f6ffed;
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
  }

  .statusIcon {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .itemLabel {
    flex: 1;
    font-size: 14px;
    color: #000000;
    line-height: 1.4;
  }

  &.running {
    border-color: #1890ff;
    background: #e6f7ff;
  }

  &.completed {
    border-color: #52c41a;
    background: #f6ffed;
  }

  &.waiting {
    border-color: #faad14;
    background: #fffbe6;
  }
}

// 状态图标样式
.statusCompleted {
  color: #52c41a;
  font-size: 18px;
}

.statusRunning {
  color: #1890ff;
  font-size: 18px;
}

.statusWaiting {
  color: #faad14;
  font-size: 18px;
}

.statusDefault {
  color: #d9d9d9;
  font-size: 18px;
}

// 响应式设计
@media (max-width: 1200px) {
  .inspectionItemWrapper {
    margin-bottom: 12px;
  }
}

@media (max-width: 768px) {
  .actionButtons {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .leftButtons,
  .rightButtons {
    width: 100%;
    text-align: center;
  }

  .inspectionItemWrapper {
    margin-bottom: 8px;
  }

  .inspectionItem {
    padding: 10px 12px;
    min-height: 44px;
  }
}

// 巡检进度样式
.progressContainer {
  padding: 20px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  border-left: 4px solid #1890ff;
}

.progressHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.progressInfo {
  flex: 1;
}

.progressText {
  font-size: 16px;
  color: #1890ff;
  margin-bottom: 8px;
  font-weight: 500;
}

.timeInfo {
  font-size: 14px;
  color: #666;
}

.stopButton {
  flex-shrink: 0;
  margin-left: 16px;
}

// 动画效果
.inspectionItem {
  transition: all 0.3s ease;
}

.collapse {
  :global(.ant-collapse-item) {
    transition: all 0.3s ease;
  }
}
