// Copyright 2021 99cloud
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import React, { useState, useEffect } from 'react';
import { Button, Collapse, Row, Col, message, Space, Progress } from 'antd';
import { CaretRightOutlined, CheckCircleOutlined, ClockCircleOutlined, LoadingOutlined } from '@ant-design/icons';
import styles from './index.less';

const { Panel } = Collapse;

export default function NewHealthInspection() {
  // 巡检项目配置数据
  const [inspectionItems, setInspectionItems] = useState({
    hostOS: {
      title: t('HostOS资源检测'),
      items: [
        { key: 'hostos_disk_usage', label: t('HostOS分区利用率检测'), checked: true },
        { key: 'hostos_cpu_usage', label: t('HostOS CPU利用率检测'), checked: true },
        { key: 'hostos_memory_usage', label: t('HostOS内存利用率检测'), checked: true },
        { key: 'hostos_alarm_event', label: t('HostOS告警事件检测'), checked: true },
        { key: 'physical_interface_check', label: t('物理网卡连通状态检测'), checked: true },
      ]
    },
    cloudPlatform: {
      title: t('云平台资源检测'),
      items: [
        { key: 'vm_status_check', label: t('平台数据库状态检测'), checked: true },
        { key: 'cinder_volume_service', label: t('CinderVolume服务'), checked: true },
        { key: 'nova_compute_service', label: t('NovaCompute服务'), checked: true },
        { key: 'neutron_openvswitch_service', label: t('NeutronOpenvswitch服务'), checked: true },
        { key: 'keepalived_service', label: t('Keepalived服务'), checked: true },
        { key: 'neutron_dhcp_service', label: t('NeutronDhcp服务'), checked: true },
        { key: 'neutron_l3_service', label: t('Neutron服务'), checked: true },
        { key: 'glance_service', label: t('Glance服务'), checked: true },
        { key: 'neutron_sserver_service', label: t('NeutronServer服务'), checked: true },
        { key: 'keystone_service', label: t('Keystone服务'), checked: true },
        { key: 'nova_service', label: t('Nova服务'), checked: true },
        { key: 'cinder_service', label: t('Cinder服务'), checked: true },
        { key: 'platform_ha_service', label: t('平台HA状态检测'), checked: true },
      ]
    },
    guestOS: {
      title: t('GuestOS资源检测'),
      items: [
        { key: 'guestos_memory_usage_check', label: t('GuestOS内存利用率检测'), checked: true },
        { key: 'guestos_cpu_usage_check', label: t('GuestOS CPU利用率检测'), checked: true },
        { key: 'various_security_checks', label: t('卷状态'), checked: true },
      ]
    },
    storage: {
      title: t('存储资源检测'),
      items: [
        { key: 'cluster_status', label: t('集群状态'), checked: true },
        { key: 'storage_pool_check', label: t('存储池状态检测'), checked: true },
        { key: 'storage_obj_status', label: t('存储对象状态'), checked: true },
        { key: 'pg_status', label: t('PG状态'), checked: true },
        { key: 'osd_status', label: t('OSD状态'), checked: true },
      ]
    },
    networkResource: {
      title: t('网络资源检测'),
      items: [
        { key: 'virtual_router_status', label: t('虚拟路由状态'), checked: true },
        { key: 'virtual_gateway_status', label: t('虚拟网关状态'), checked: true },
        { key: 'port_status', label: t('端口状态'), checked: true },
        { key: 'host_packet_loss_detection', label: t('主机丢包检测'), checked: true },
        { key: 'response_content_health_check', label: t('基于响应内容的健康检查'), checked: true },
      ]
    }
  });

  // 巡检状态
  const [isInspecting, setIsInspecting] = useState(false);
  const [inspectionProgress, setInspectionProgress] = useState(0);
  const [completedCount, setCompletedCount] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [startTime, setStartTime] = useState(null);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [currentInspectingItem, setCurrentInspectingItem] = useState(null);
  const [itemStatuses, setItemStatuses] = useState({});

  // 全局控制状态
  const [allChecked, setAllChecked] = useState(true);
  const [indeterminate, setIndeterminate] = useState(false);
  const [allCollapsed, setAllCollapsed] = useState(false);
  const [activeKeys, setActiveKeys] = useState(['hostOS', 'cloudPlatform', 'guestOS', 'storage', 'networkResource']);

  // 处理单个巡检项的选择
  const handleItemCheck = (category, itemKey, checked) => {
    const newInspectionItems = {
      ...inspectionItems,
      [category]: {
        ...inspectionItems[category],
        items: inspectionItems[category].items.map(item =>
          item.key === itemKey ? { ...item, checked } : item
        )
      }
    };

    setInspectionItems(newInspectionItems);
    updateGlobalCheckState(newInspectionItems);
  };

  // 初始化时更新全局状态
  useEffect(() => {
    updateGlobalCheckState();
  }, []);

  // 计时器效果
  useEffect(() => {
    let timer;
    if (isInspecting && startTime) {
      timer = setInterval(() => {
        setElapsedTime(Math.floor((Date.now() - startTime) / 1000));
      }, 1000);
    }
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [isInspecting, startTime]);

  // 更新全局选择状态
  const updateGlobalCheckState = (newInspectionItems = null) => {
    const itemsToCheck = newInspectionItems || inspectionItems;
    const allItems = Object.values(itemsToCheck).flatMap(category => category.items);
    const checkedItems = allItems.filter(item => item.checked);
    const allItemsChecked = checkedItems.length === allItems.length;
    const someItemsChecked = checkedItems.length > 0;

    setAllChecked(allItemsChecked);
    setIndeterminate(someItemsChecked && !allItemsChecked);
  };

  // 处理全局全选/取消全选
  const handleGlobalCheckAll = () => {
    // 如果当前是全选状态（没有indeterminate），则取消全选
    // 否则（部分选中或未选中），则全选
    const shouldSelectAll = !allChecked || indeterminate;

    setAllChecked(shouldSelectAll);
    setIndeterminate(false);
    setInspectionItems(prev => {
      const newItems = {};
      Object.keys(prev).forEach(category => {
        newItems[category] = {
          ...prev[category],
          items: prev[category].items.map(item => ({ ...item, checked: shouldSelectAll }))
        };
      });
      return newItems;
    });
  };

  // 处理收起/展开所有
  const handleToggleAll = () => {
    if (allCollapsed) {
      setActiveKeys(['hostOS', 'cloudPlatform', 'guestOS', 'storage', 'networkResource']);
      setAllCollapsed(false);
    } else {
      setActiveKeys([]);
      setAllCollapsed(true);
    }
  };

  // 处理折叠面板变化
  const handleCollapseChange = (keys) => {
    setActiveKeys(keys);
    setAllCollapsed(keys.length === 0);
  };



  // 格式化时间显示
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // 获取项目状态
  const getItemStatus = (category, itemKey) => {
    const itemId = `${category}_${itemKey}`;
    return itemStatuses[itemId] || 'idle';
  };

  // 渲染状态图标
  const renderStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircleOutlined className={styles.statusCompleted} />;
      case 'running':
        return <LoadingOutlined className={styles.statusRunning} spin />;
      case 'waiting':
        return <ClockCircleOutlined className={styles.statusWaiting} />;
      default:
        return null;
    }
  };

  // 开始巡检
  const handleStartInspection = () => {
    // 获取所有巡检项（现在不需要选择，全部执行）
    const allItems = [];
    Object.keys(inspectionItems).forEach(category => {
      inspectionItems[category].items.forEach(item => {
        allItems.push({
          category,
          key: item.key,
          label: item.label
        });
      });
    });

    // 初始化巡检状态
    setIsInspecting(true);
    setInspectionProgress(0);
    setCompletedCount(0);
    setTotalCount(allItems.length);
    setStartTime(Date.now());
    setElapsedTime(0);
    setCurrentInspectingItem(null);

    // 初始化所有项目状态为等待中
    const initialStatuses = {};
    allItems.forEach(item => {
      const itemId = `${item.category}_${item.key}`;
      initialStatuses[itemId] = 'waiting';
    });
    setItemStatuses(initialStatuses);

    // 开始逐个执行巡检
    executeInspectionSequence(allItems, 0);
    message.info(t('Inspection started, please wait...'));
  };

  // 执行巡检序列
  const executeInspectionSequence = (selectedItems, currentIndex) => {
    if (currentIndex >= selectedItems.length) {
      // 所有项目完成
      setIsInspecting(false);
      setCurrentInspectingItem(null);
      setInspectionProgress(100);
      message.success(t('Inspection completed successfully'));
      return;
    }

    const currentItem = selectedItems[currentIndex];
    const itemId = `${currentItem.category}_${currentItem.key}`;

    // 设置当前项目为进行中
    setCurrentInspectingItem(itemId);
    setItemStatuses(prev => ({
      ...prev,
      [itemId]: 'running'
    }));

    // 模拟巡检时间（1-3秒随机）
    const inspectionTime = Math.random() * 2000 + 1000;

    setTimeout(() => {
      // 完成当前项目
      setItemStatuses(prev => ({
        ...prev,
        [itemId]: 'completed'
      }));

      const newCompletedCount = currentIndex + 1;
      setCompletedCount(newCompletedCount);
      setInspectionProgress(Math.round((newCompletedCount / selectedItems.length) * 100));

      // 继续下一个项目
      executeInspectionSequence(selectedItems, currentIndex + 1);
    }, inspectionTime);
  };

  // 停止巡检
  const handleStopInspection = () => {
    setIsInspecting(false);
    setCurrentInspectingItem(null);
    setItemStatuses({});
    message.info(t('Inspection stopped'));
  };

  // 处理分类全选/取消全选
  const handleCategoryCheckAll = (category, checked) => {
    const newInspectionItems = {
      ...inspectionItems,
      [category]: {
        ...inspectionItems[category],
        items: inspectionItems[category].items.map(item => ({ ...item, checked }))
      }
    };

    setInspectionItems(newInspectionItems);
    updateGlobalCheckState(newInspectionItems);
  };

  // 检查分类是否全选
  const isCategoryAllChecked = (category) => {
    const items = inspectionItems[category].items;
    return items.length > 0 && items.every(item => item.checked);
  };

  // 检查分类是否部分选中
  const isCategoryIndeterminate = (category) => {
    const items = inspectionItems[category].items;
    const checkedItems = items.filter(item => item.checked);
    return checkedItems.length > 0 && checkedItems.length < items.length;
  };

  // 渲染巡检项目面板
  const renderInspectionPanel = (category, data) => {
    const { title, items } = data;
    const categoryAllChecked = isCategoryAllChecked(category);
    const indeterminate = isCategoryIndeterminate(category);

    return (
      <Panel
        header={
          <div className={styles.panelHeader}>
            <span className={styles.categoryTitleText}>
              {title}
            </span>
          </div>
        }
        key={category}
        className={styles.inspectionPanel}
      >
        <Row gutter={[16, 12]}>
          {items.map(item => {
            const status = getItemStatus(category, item.key);
            const itemClass = `${styles.inspectionItem} ${status ? styles[status] : ''}`;
            return (
              <Col xs={24} sm={12} md={8} lg={6} xl={6} key={item.key}>
                <div className={styles.inspectionItemWrapper}>
                  <div className={itemClass}>
                    <div className={styles.statusIcon}>
                      {renderStatusIcon(status)}
                    </div>
                    <span className={styles.itemLabel}>{item.label}</span>
                  </div>
                </div>
              </Col>
            );
          })}
        </Row>
      </Panel>
    );
  };

  return (
    <div className={styles.container}>
      {/* 页面头部 */}
      <div className={styles.header}>
        <div className={styles.titleSection}>
          <h1 className={styles.title}>
            <span className={styles.titleMain}>{t('New Health Inspection')}</span>
            <span className={styles.titleDescription}>{t('Configure platform-wide health inspection work items to ensure normal operation of various health indicators of the platform')}</span>
          </h1>
        </div>
        
        <div className={styles.actionButtons}>
          <div className={styles.leftButtons}>
            <Button
              type="primary"
              size="large"
              loading={isInspecting}
              onClick={handleStartInspection}
              disabled={isInspecting}
            >
              {isInspecting ? `${t('Inspecting')}... ${inspectionProgress}%` : t('Start Detection')}
            </Button>
          </div>
          <div className={styles.rightButtons}>
            <Space>
              <Button size="large">
                {t('Inspection Configuration')}
              </Button>
              <Button size="large">
                {t('Inspection Records')}
              </Button>
            </Space>
          </div>
        </div>
      </div>

      {/* 巡检进度显示 */}
      {isInspecting && (
        <div className={styles.progressContainer}>
          <div className={styles.progressHeader}>
            <div className={styles.progressInfo}>
              <div className={styles.progressText}>
                {t('Inspecting')}... {completedCount}/{totalCount} ({inspectionProgress}%)
              </div>
              <div className={styles.timeInfo}>
                {t('Elapsed Time')}: {formatTime(elapsedTime)}
              </div>
            </div>
            <Button
              danger
              onClick={handleStopInspection}
              className={styles.stopButton}
            >
              {t('Stop Detection')}
            </Button>
          </div>
          <Progress
            percent={inspectionProgress}
            status={inspectionProgress === 100 ? 'success' : 'active'}
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
            showInfo={false}
            strokeWidth={8}
            trailColor="#f0f0f0"
          />
        </div>
      )}

      {/* 巡检项目配置 */}
      <div className={styles.content}>
        <div className={styles.inspectionConfig}>
          {/* 全局控制栏 */}
          <div className={styles.globalControls}>
            <div className={styles.globalTitle}>
              {t('Inspection Items')}
            </div>
            <div className={styles.globalButtons}>
              <Button
                size="small"
                onClick={handleToggleAll}
              >
                {allCollapsed ? t('Expand All') : t('Collapse All')}
              </Button>
            </div>
          </div>

          {/* 折叠面板 */}
          <Collapse
            activeKey={activeKeys}
            onChange={handleCollapseChange}
            expandIcon={({ isActive }) => (
              <CaretRightOutlined rotate={isActive ? 90 : 0} />
            )}
            className={styles.collapse}
          >
            {Object.keys(inspectionItems).map(category =>
              renderInspectionPanel(category, inspectionItems[category])
            )}
          </Collapse>
        </div>
      </div>
    </div>
  );
}
