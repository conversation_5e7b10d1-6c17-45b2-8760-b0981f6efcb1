// Copyright 2021 99cloud
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import React, { useState, useEffect } from 'react';
import { Button, Collapse, Checkbox, Row, Col, message, Space, Progress } from 'antd';
import { CaretRightOutlined } from '@ant-design/icons';
import styles from './index.less';

const { Panel } = Collapse;

export default function NewHealthInspection() {
  // 巡检项目配置数据
  const [inspectionItems, setInspectionItems] = useState({
    hostOS: {
      title: t('HostOS资源检测'),
      items: [
        { key: 'hostos_disk_usage', label: t('HostOS分区利用率检测'), checked: true },
        { key: 'hostos_cpu_usage', label: t('HostOS CPU利用率检测'), checked: true },
        { key: 'hostos_memory_usage', label: t('HostOS内存利用率检测'), checked: true },
        { key: 'hostos_alarm_event', label: t('HostOS告警事件检测'), checked: true },
        { key: 'physical_interface_check', label: t('物理网卡连通状态检测'), checked: true },
      ]
    },
    cloudPlatform: {
      title: t('云平台资源检测'),
      items: [
        { key: 'vm_status_check', label: t('平台数据库状态检测'), checked: true },
        { key: 'cinder_volume_service', label: t('CinderVolume服务'), checked: true },
        { key: 'nova_compute_service', label: t('NovaCompute服务'), checked: true },
        { key: 'neutron_openvswitch_service', label: t('NeutronOpenvswitch服务'), checked: true },
        { key: 'keepalived_service', label: t('Keepalived服务'), checked: true },
        { key: 'neutron_dhcp_service', label: t('NeutronDhcp服务'), checked: true },
        { key: 'neutron_l3_service', label: t('Neutron服务'), checked: true },
        { key: 'glance_service', label: t('Glance服务'), checked: true },
        { key: 'neutron_sserver_service', label: t('NeutronServer服务'), checked: true },
        { key: 'keystone_service', label: t('Keystone服务'), checked: true },
        { key: 'nova_service', label: t('Nova服务'), checked: true },
        { key: 'cinder_service', label: t('Cinder服务'), checked: true },
        { key: 'platform_ha_service', label: t('平台HA状态检测'), checked: true },
      ]
    },
    guestOS: {
      title: t('GuestOS资源检测'),
      items: [
        { key: 'guestos_memory_usage_check', label: t('GuestOS内存利用率检测'), checked: true },
        { key: 'guestos_cpu_usage_check', label: t('GuestOS CPU利用率检测'), checked: true },
        { key: 'various_security_checks', label: t('卷状态'), checked: true },
      ]
    },
    storage: {
      title: t('存储资源检测'),
      items: [
        { key: 'cluster_status', label: t('集群状态'), checked: true },
        { key: 'storage_pool_check', label: t('存储池状态检测'), checked: true },
        { key: 'storage_obj_status', label: t('存储对象状态'), checked: true },
        { key: 'pg_status', label: t('PG状态'), checked: true },
        { key: 'osd_status', label: t('OSD状态'), checked: true },
      ]
    },
    networkResource: {
      title: t('网络资源检测'),
      items: [
        { key: 'virtual_router_status', label: t('虚拟路由状态'), checked: true },
        { key: 'virtual_gateway_status', label: t('虚拟网关状态'), checked: true },
        { key: 'port_status', label: t('端口状态'), checked: true },
        { key: 'host_packet_loss_detection', label: t('主机丢包检测'), checked: true },
        { key: 'response_content_health_check', label: t('基于响应内容的健康检查'), checked: true },
      ]
    }
  });

  // 巡检状态
  const [isInspecting, setIsInspecting] = useState(false);
  const [inspectionProgress, setInspectionProgress] = useState(0);

  // 全局控制状态
  const [allChecked, setAllChecked] = useState(true);
  const [indeterminate, setIndeterminate] = useState(false);
  const [allCollapsed, setAllCollapsed] = useState(false);
  const [activeKeys, setActiveKeys] = useState(['hostOS', 'cloudPlatform', 'guestOS', 'storage', 'networkResource']);

  // 处理单个巡检项的选择
  const handleItemCheck = (category, itemKey, checked) => {
    const newInspectionItems = {
      ...inspectionItems,
      [category]: {
        ...inspectionItems[category],
        items: inspectionItems[category].items.map(item =>
          item.key === itemKey ? { ...item, checked } : item
        )
      }
    };

    setInspectionItems(newInspectionItems);
    updateGlobalCheckState(newInspectionItems);
  };

  // 初始化时更新全局状态
  useEffect(() => {
    updateGlobalCheckState();
  }, []);

  // 更新全局选择状态
  const updateGlobalCheckState = (newInspectionItems = null) => {
    const itemsToCheck = newInspectionItems || inspectionItems;
    const allItems = Object.values(itemsToCheck).flatMap(category => category.items);
    const checkedItems = allItems.filter(item => item.checked);
    const allItemsChecked = checkedItems.length === allItems.length;
    const someItemsChecked = checkedItems.length > 0;

    setAllChecked(allItemsChecked);
    setIndeterminate(someItemsChecked && !allItemsChecked);
  };

  // 处理全局全选/取消全选
  const handleGlobalCheckAll = () => {
    // 如果当前是全选状态（没有indeterminate），则取消全选
    // 否则（部分选中或未选中），则全选
    const shouldSelectAll = !allChecked || indeterminate;

    setAllChecked(shouldSelectAll);
    setIndeterminate(false);
    setInspectionItems(prev => {
      const newItems = {};
      Object.keys(prev).forEach(category => {
        newItems[category] = {
          ...prev[category],
          items: prev[category].items.map(item => ({ ...item, checked: shouldSelectAll }))
        };
      });
      return newItems;
    });
  };

  // 处理收起/展开所有
  const handleToggleAll = () => {
    if (allCollapsed) {
      setActiveKeys(['hostOS', 'cloudPlatform', 'guestOS', 'storage', 'networkResource']);
      setAllCollapsed(false);
    } else {
      setActiveKeys([]);
      setAllCollapsed(true);
    }
  };

  // 处理折叠面板变化
  const handleCollapseChange = (keys) => {
    setActiveKeys(keys);
    setAllCollapsed(keys.length === 0);
  };



  // 开始巡检
  const handleStartInspection = () => {
    // 获取所有选中的巡检项
    const selectedItems = [];
    Object.keys(inspectionItems).forEach(category => {
      inspectionItems[category].items.forEach(item => {
        if (item.checked) {
          selectedItems.push({
            category,
            key: item.key,
            label: item.label
          });
        }
      });
    });

    if (selectedItems.length === 0) {
      message.warning(t('Please select at least one inspection item'));
      return;
    }

    setIsInspecting(true);
    setInspectionProgress(0);
    
    // 模拟巡检进度
    const progressInterval = setInterval(() => {
      setInspectionProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval);
          setIsInspecting(false);
          message.success(t('Inspection completed successfully'));
          return 100;
        }
        return prev + 10;
      });
    }, 500);

    message.info(t('Inspection started, please wait...'));
  };

  // 处理分类全选/取消全选
  const handleCategoryCheckAll = (category, checked) => {
    const newInspectionItems = {
      ...inspectionItems,
      [category]: {
        ...inspectionItems[category],
        items: inspectionItems[category].items.map(item => ({ ...item, checked }))
      }
    };

    setInspectionItems(newInspectionItems);
    updateGlobalCheckState(newInspectionItems);
  };

  // 检查分类是否全选
  const isCategoryAllChecked = (category) => {
    const items = inspectionItems[category].items;
    return items.length > 0 && items.every(item => item.checked);
  };

  // 检查分类是否部分选中
  const isCategoryIndeterminate = (category) => {
    const items = inspectionItems[category].items;
    const checkedItems = items.filter(item => item.checked);
    return checkedItems.length > 0 && checkedItems.length < items.length;
  };

  // 渲染巡检项目面板
  const renderInspectionPanel = (category, data) => {
    const { title, items } = data;
    const categoryAllChecked = isCategoryAllChecked(category);
    const indeterminate = isCategoryIndeterminate(category);

    return (
      <Panel
        header={
          <div className={styles.panelHeader}>
            <Checkbox
              checked={categoryAllChecked}
              indeterminate={indeterminate}
              onChange={(e) => handleCategoryCheckAll(category, e.target.checked)}
              onClick={(e) => e.stopPropagation()}
            />
            <span
              className={styles.categoryTitleText}
              onClick={(e) => {
                e.stopPropagation();
                handleCategoryCheckAll(category, !categoryAllChecked);
              }}
            >
              {title}
            </span>
          </div>
        }
        key={category}
        className={styles.inspectionPanel}
      >
        <Row gutter={[16, 12]}>
          {items.map(item => (
            <Col xs={24} sm={12} md={8} lg={6} xl={6} key={item.key}>
              <div className={styles.inspectionItemWrapper}>
                <Checkbox
                  checked={item.checked}
                  onChange={(e) => handleItemCheck(category, item.key, e.target.checked)}
                  className={styles.inspectionItem}
                >
                  {item.label}
                </Checkbox>
              </div>
            </Col>
          ))}
        </Row>
      </Panel>
    );
  };

  return (
    <div className={styles.container}>
      {/* 页面头部 */}
      <div className={styles.header}>
        <div className={styles.titleSection}>
          <h1 className={styles.title}>
            <span className={styles.titleMain}>{t('New Health Inspection')}</span>
            <span className={styles.titleDescription}>{t('Configure platform-wide health inspection work items to ensure normal operation of various health indicators of the platform')}</span>
          </h1>
        </div>
        
        <div className={styles.actionButtons}>
          <div className={styles.leftButtons}>
            <Button
              type="primary"
              size="large"
              loading={isInspecting}
              onClick={handleStartInspection}
              disabled={isInspecting}
            >
              {isInspecting ? `${t('Inspecting')}... ${inspectionProgress}%` : t('Start Detection')}
            </Button>
          </div>
          <div className={styles.rightButtons}>
            <Space>
              <Button size="large">
                {t('Inspection Configuration')}
              </Button>
              <Button size="large">
                {t('Inspection Records')}
              </Button>
            </Space>
          </div>
        </div>
      </div>

      {/* 巡检进度显示 */}
      {isInspecting && (
        <div className={styles.progressContainer}>
          <div className={styles.progressText}>
            {t('Inspecting')}... {inspectionProgress}%
          </div>
          <Progress
            percent={inspectionProgress}
            status={inspectionProgress === 100 ? 'success' : 'active'}
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
          />
        </div>
      )}

      {/* 巡检项目配置 */}
      <div className={styles.content}>
        <div className={styles.inspectionConfig}>
          {/* 全局控制栏 */}
          <div className={styles.globalControls}>
            <div className={styles.globalCheckbox}>
              <Checkbox
                checked={allChecked}
                indeterminate={indeterminate}
                onChange={handleGlobalCheckAll}
              >
                {allChecked && !indeterminate ? t('Unselect All') : t('Select All')}
              </Checkbox>
            </div>
            <div className={styles.globalButtons}>
              <Button
                size="small"
                onClick={handleToggleAll}
              >
                {allCollapsed ? t('Expand All') : t('Collapse All')}
              </Button>
            </div>
          </div>

          {/* 折叠面板 */}
          <Collapse
            activeKey={activeKeys}
            onChange={handleCollapseChange}
            expandIcon={({ isActive }) => (
              <CaretRightOutlined rotate={isActive ? 90 : 0} />
            )}
            className={styles.collapse}
          >
            {Object.keys(inspectionItems).map(category =>
              renderInspectionPanel(category, inspectionItems[category])
            )}
          </Collapse>
        </div>
      </div>
    </div>
  );
}
