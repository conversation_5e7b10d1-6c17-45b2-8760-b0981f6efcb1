// Copyright 2021 99cloud
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import React, { useState, useEffect } from 'react';
import { Button, Collapse, Checkbox, message, Space, Progress } from 'antd';
import { CaretRightOutlined, CheckCircleOutlined, LoadingOutlined } from '@ant-design/icons';
import styles from './index.less';

const { Panel } = Collapse;

export default function NewHealthInspection() {
  // 巡检项目配置数据
  const [inspectionItems, setInspectionItems] = useState({
    hostOS: {
      title: t('HostOS资源检测'),
      items: [
        { key: 'hostos_disk_usage', label: t('HostOS分区利用率检测'), checked: true },
        { key: 'hostos_cpu_usage', label: t('HostOS CPU利用率检测'), checked: true },
        { key: 'hostos_memory_usage', label: t('HostOS内存利用率检测'), checked: true },
        { key: 'hostos_alarm_event', label: t('HostOS告警事件检测'), checked: true },
        { key: 'physical_interface_check', label: t('物理网卡连通状态检测'), checked: true },
      ]
    },
    cloudPlatform: {
      title: t('云平台资源检测'),
      items: [
        { key: 'vm_status_check', label: t('平台数据库状态检测'), checked: true },
        { key: 'cinder_volume_service', label: t('CinderVolume服务'), checked: true },
        { key: 'nova_compute_service', label: t('NovaCompute服务'), checked: true },
        { key: 'neutron_openvswitch_service', label: t('NeutronOpenvswitch服务'), checked: true },
        { key: 'keepalived_service', label: t('Keepalived服务'), checked: true },
        { key: 'neutron_dhcp_service', label: t('NeutronDhcp服务'), checked: true },
        { key: 'neutron_l3_service', label: t('Neutron服务'), checked: true },
        { key: 'glance_service', label: t('Glance服务'), checked: true },
        { key: 'neutron_sserver_service', label: t('NeutronServer服务'), checked: true },
        { key: 'keystone_service', label: t('Keystone服务'), checked: true },
        { key: 'nova_service', label: t('Nova服务'), checked: true },
        { key: 'cinder_service', label: t('Cinder服务'), checked: true },
        { key: 'platform_ha_service', label: t('平台HA状态检测'), checked: true },
      ]
    },
    guestOS: {
      title: t('GuestOS资源检测'),
      items: [
        { key: 'guestos_memory_usage_check', label: t('GuestOS内存利用率检测'), checked: true },
        { key: 'guestos_cpu_usage_check', label: t('GuestOS CPU利用率检测'), checked: true },
        { key: 'various_security_checks', label: t('卷状态'), checked: true },
      ]
    },
    storage: {
      title: t('存储资源检测'),
      items: [
        { key: 'cluster_status', label: t('集群状态'), checked: true },
        { key: 'storage_pool_check', label: t('存储池状态检测'), checked: true },
        { key: 'storage_obj_status', label: t('存储对象状态'), checked: true },
        { key: 'pg_status', label: t('PG状态'), checked: true },
        { key: 'osd_status', label: t('OSD状态'), checked: true },
      ]
    },
    networkResource: {
      title: t('网络资源检测'),
      items: [
        { key: 'virtual_router_status', label: t('虚拟路由状态'), checked: true },
        { key: 'virtual_gateway_status', label: t('虚拟网关状态'), checked: true },
        { key: 'port_status', label: t('端口状态'), checked: true },
        { key: 'host_packet_loss_detection', label: t('主机丢包检测'), checked: true },
        { key: 'response_content_health_check', label: t('基于响应内容的健康检查'), checked: true },
      ]
    }
  });

  // 巡检状态
  const [isInspecting, setIsInspecting] = useState(false);
  const [inspectionProgress, setInspectionProgress] = useState(0);
  const [startTime, setStartTime] = useState(null);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [itemStatuses, setItemStatuses] = useState({});

  // 全局控制状态
  const [allChecked, setAllChecked] = useState(true);
  const [indeterminate, setIndeterminate] = useState(false);
  const [allCollapsed, setAllCollapsed] = useState(false);
  const [activeKeys, setActiveKeys] = useState(['hostOS', 'cloudPlatform', 'guestOS', 'storage', 'networkResource']);

  // 处理单个巡检项的选择
  const handleItemCheck = (category, itemKey, checked) => {
    const newInspectionItems = {
      ...inspectionItems,
      [category]: {
        ...inspectionItems[category],
        items: inspectionItems[category].items.map(item =>
          item.key === itemKey ? { ...item, checked } : item
        )
      }
    };

    setInspectionItems(newInspectionItems);
    updateGlobalCheckState(newInspectionItems);
  };

  // 初始化项目状态
  useEffect(() => {
    // 页面加载时不设置任何状态，保持空对象
    setItemStatuses({});
    updateGlobalCheckState();
  }, []);

  // 更新全局选择状态
  const updateGlobalCheckState = (newInspectionItems = null) => {
    const itemsToCheck = newInspectionItems || inspectionItems;
    const allItems = Object.values(itemsToCheck).flatMap(category => category.items);
    const checkedItems = allItems.filter(item => item.checked);
    const allItemsChecked = checkedItems.length === allItems.length;
    const someItemsChecked = checkedItems.length > 0;

    setAllChecked(allItemsChecked);
    setIndeterminate(someItemsChecked && !allItemsChecked);
  };

  // 计时器效果
  useEffect(() => {
    let timer;
    if (isInspecting && startTime) {
      timer = setInterval(() => {
        setElapsedTime(Math.floor((Date.now() - startTime) / 1000));
      }, 1000);
    }
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [isInspecting, startTime]);



  // 处理全局全选/取消全选
  const handleGlobalCheckAll = () => {
    // 如果当前是全选状态（没有indeterminate），则取消全选
    // 否则（部分选中或未选中），则全选
    const shouldSelectAll = !allChecked || indeterminate;

    setAllChecked(shouldSelectAll);
    setIndeterminate(false);
    setInspectionItems(prev => {
      const newItems = {};
      Object.keys(prev).forEach(category => {
        newItems[category] = {
          ...prev[category],
          items: prev[category].items.map(item => ({ ...item, checked: shouldSelectAll }))
        };
      });
      return newItems;
    });
  };

  // 处理收起/展开所有
  const handleToggleAll = () => {
    if (allCollapsed) {
      setActiveKeys(['hostOS', 'cloudPlatform', 'guestOS', 'storage', 'networkResource']);
      setAllCollapsed(false);
    } else {
      setActiveKeys([]);
      setAllCollapsed(true);
    }
  };

  // 处理折叠面板变化
  const handleCollapseChange = (keys) => {
    setActiveKeys(keys);
    setAllCollapsed(keys.length === 0);
  };



  // 格式化时间显示
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // 获取项目状态
  const getItemStatus = (category, itemKey) => {
    const itemId = `${category}_${itemKey}`;
    return itemStatuses[itemId] || null;
  };

  // 渲染状态图标
  const renderStatusIcon = (status) => {
    // 如果没有开始巡检，不显示任何图标
    if (!isInspecting) {
      return null;
    }

    // 巡检进行中时，根据状态显示图标
    switch (status) {
      case 'completed':
        return <CheckCircleOutlined className={styles.statusCompleted} />;
      case 'running':
        return <LoadingOutlined className={styles.statusRunning} spin />;
      case 'waiting':
      default:
        return <LoadingOutlined className={styles.statusWaiting} spin />;
    }
  };

  // 开始巡检
  const handleStartInspection = () => {
    // 获取所有选中的巡检项
    const selectedItems = [];
    Object.keys(inspectionItems).forEach(category => {
      inspectionItems[category].items.forEach(item => {
        if (item.checked) {
          selectedItems.push({
            category,
            key: item.key,
            label: item.label
          });
        }
      });
    });

    if (selectedItems.length === 0) {
      message.warning(t('Please select at least one inspection item'));
      return;
    }

    // 初始化巡检状态
    setIsInspecting(true);
    setInspectionProgress(0);
    setStartTime(Date.now());
    setElapsedTime(0);

    // 初始化所有选中项目状态为等待中（显示转动特效）
    const initialStatuses = {};
    selectedItems.forEach(item => {
      const itemId = `${item.category}_${item.key}`;
      initialStatuses[itemId] = 'waiting';
    });
    setItemStatuses(initialStatuses);

    // 开始逐个执行巡检
    executeInspectionSequence(selectedItems, 0);
    message.info(t('Inspection started, please wait...'));
  };

  // 执行巡检序列
  const executeInspectionSequence = (selectedItems, currentIndex) => {
    if (currentIndex >= selectedItems.length) {
      // 所有项目完成
      setIsInspecting(false);
      setCurrentInspectingItem(null);
      setInspectionProgress(100);
      message.success(t('Inspection completed successfully'));

      // 生成巡检结果数据
      const inspectionData = generateInspectionResult(selectedItems);

      // 跳转到结果页面
      // 将数据存储到sessionStorage中，然后跳转
      sessionStorage.setItem('inspectionData', JSON.stringify(inspectionData));
      window.location.href = '/base/health-inspection-result-admin';
      return;
    }

    const currentItem = selectedItems[currentIndex];
    const itemId = `${currentItem.category}_${currentItem.key}`;

    // 设置当前项目为进行中
    setItemStatuses(prev => ({
      ...prev,
      [itemId]: 'running'
    }));

    // 模拟巡检时间（1-3秒随机）
    const inspectionTime = Math.random() * 2000 + 1000;

    setTimeout(() => {
      // 完成当前项目
      setItemStatuses(prev => ({
        ...prev,
        [itemId]: 'completed'
      }));

      const newCompletedCount = currentIndex + 1;
      setInspectionProgress(Math.round((newCompletedCount / selectedItems.length) * 100));

      // 继续下一个项目
      executeInspectionSequence(selectedItems, currentIndex + 1);
    }, inspectionTime);
  };

  // 生成巡检结果数据
  const generateInspectionResult = (selectedItems) => {
    const endTime = new Date();
    const duration = Math.floor((endTime - startTime) / 1000);

    // 模拟一些异常结果
    const errorItems = [
      {
        id: 1,
        category: '存储资源检测',
        name: '集群状态',
        status: 'error',
        message: 'UnknownHostException: null',
        details: '集群连接异常，请检查网络连接状态。'
      },
      {
        id: 2,
        category: '存储资源检测',
        name: '存储状态安全检查',
        status: 'error',
        message: 'Connection timeout',
        details: '存储服务连接超时，请检查存储服务状态。'
      }
    ];

    const normalItems = selectedItems.length - errorItems.length;

    return {
      totalItems: selectedItems.length,
      normalItems: normalItems,
      warningItems: 0,
      errorItems: errorItems.length,
      startTime: new Date(startTime).toLocaleString('zh-CN'),
      endTime: endTime.toLocaleString('zh-CN'),
      duration: formatTime(duration),
      results: errorItems
    };
  };

  // 停止巡检
  const handleStopInspection = () => {
    setIsInspecting(false);
    setItemStatuses({});
    message.info(t('Inspection stopped'));
  };



  // 渲染巡检项目面板
  const renderInspectionPanel = (category, data) => {
    const { title, items } = data;

    return (
      <Panel
        header={
          <div className={styles.panelHeader}>
            <span className={styles.categoryTitleText}>
              {title}
            </span>
          </div>
        }
        key={category}
        className={styles.inspectionPanel}
      >
        <div className={styles.inspectionGrid}>
          {items.map(item => {
            const status = getItemStatus(category, item.key);
            const itemClass = `${styles.inspectionItem} ${status ? styles[status] : ''}`;
            return (
              <div className={styles.inspectionItemWrapper} key={item.key}>
                <div className={itemClass}>
                  <Checkbox
                    checked={item.checked}
                    onChange={(e) => handleItemCheck(category, item.key, e.target.checked)}
                    disabled={isInspecting}
                  />
                  <div className={styles.statusIcon}>
                    {renderStatusIcon(status)}
                  </div>
                  <span className={styles.itemLabel}>{item.label}</span>
                </div>
              </div>
            );
          })}
        </div>
      </Panel>
    );
  };

  return (
    <div className={styles.container}>
      {/* 页面头部 - 只在未巡检时显示 */}
      {!isInspecting && (
        <div className={styles.header}>
          <div className={styles.titleSection}>
            <h1 className={styles.title}>
              <span className={styles.titleMain}>{t('New Health Inspection')}</span>
              <span className={styles.titleDescription}>{t('Configure platform-wide health inspection work items to ensure normal operation of various health indicators of the platform')}</span>
            </h1>
          </div>

          <div className={styles.actionButtons}>
            <div className={styles.leftButtons}>
              <Button
                type="primary"
                size="large"
                onClick={handleStartInspection}
              >
                {t('Start Detection')}
              </Button>
            </div>
            <div className={styles.rightButtons}>
              <Space>
                <Button size="large">
                  {t('Inspection Configuration')}
                </Button>
                <Button size="large" onClick={() => window.location.href = '/base/health-inspection-result-admin'}>
                  {t('View Inspection Results')}
                </Button>
              </Space>
            </div>
          </div>
        </div>
      )}

      {/* 巡检进度显示 */}
      {isInspecting && (
        <div className={styles.progressContainer}>
          <div className={styles.progressHeader}>
            <div className={styles.progressInfo}>
              <div className={styles.progressText}>
                {t('We are inspecting your cloud platform, please wait')}... ({inspectionProgress}%)
              </div>
              <div className={styles.timeInfo}>
                {t('Elapsed Time')}: {formatTime(elapsedTime)}
              </div>
            </div>
          </div>
          <div className={styles.progressSection}>
            <div className={styles.progressWrapper}>
              <Progress
                percent={inspectionProgress}
                status={inspectionProgress === 100 ? 'success' : 'active'}
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
                showInfo={true}
                strokeWidth={8}
                trailColor="#f0f0f0"
                format={(percent) => `${percent}%`}
              />
            </div>
            <Button
              danger
              onClick={handleStopInspection}
              className={styles.stopButton}
            >
              {t('Stop Detection')}
            </Button>
          </div>
        </div>
      )}

      {/* 巡检项目配置 */}
      <div className={styles.content}>
        <div className={styles.inspectionConfig}>
          {/* 全局控制栏 */}
          <div className={styles.globalControls}>
            <div className={styles.globalCheckbox}>
              <Checkbox
                checked={allChecked}
                indeterminate={indeterminate}
                onChange={handleGlobalCheckAll}
                disabled={isInspecting}
              >
                {allChecked && !indeterminate ? t('Unselect All') : t('Select All')}
              </Checkbox>
            </div>
            <div className={styles.globalButtons}>
              <Button
                size="small"
                onClick={handleToggleAll}
              >
                {allCollapsed ? t('Expand All') : t('Collapse All')}
              </Button>
            </div>
          </div>

          {/* 折叠面板 */}
          <Collapse
            activeKey={activeKeys}
            onChange={handleCollapseChange}
            expandIcon={({ isActive }) => (
              <CaretRightOutlined rotate={isActive ? 90 : 0} />
            )}
            className={styles.collapse}
          >
            {Object.keys(inspectionItems).map(category =>
              renderInspectionPanel(category, inspectionItems[category])
            )}
          </Collapse>
        </div>
      </div>
    </div>
  );
}
