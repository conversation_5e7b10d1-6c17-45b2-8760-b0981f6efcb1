// Copyright 2021 99cloud
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import React, { useState, useEffect } from 'react';
import { Button, Collapse, Row, Col, message, Space, Progress } from 'antd';
import { CaretRightOutlined, CheckCircleOutlined, LoadingOutlined } from '@ant-design/icons';
import styles from './index.less';

const { Panel } = Collapse;

export default function NewHealthInspection() {
  // 巡检项目配置数据
  const [inspectionItems, setInspectionItems] = useState({
    hostOS: {
      title: t('HostOS资源检测'),
      items: [
        { key: 'hostos_disk_usage', label: t('HostOS分区利用率检测') },
        { key: 'hostos_cpu_usage', label: t('HostOS CPU利用率检测') },
        { key: 'hostos_memory_usage', label: t('HostOS内存利用率检测') },
        { key: 'hostos_alarm_event', label: t('HostOS告警事件检测') },
        { key: 'physical_interface_check', label: t('物理网卡连通状态检测') },
      ]
    },
    cloudPlatform: {
      title: t('云平台资源检测'),
      items: [
        { key: 'vm_status_check', label: t('平台数据库状态检测') },
        { key: 'cinder_volume_service', label: t('CinderVolume服务') },
        { key: 'nova_compute_service', label: t('NovaCompute服务') },
        { key: 'neutron_openvswitch_service', label: t('NeutronOpenvswitch服务') },
        { key: 'keepalived_service', label: t('Keepalived服务') },
        { key: 'neutron_dhcp_service', label: t('NeutronDhcp服务') },
        { key: 'neutron_l3_service', label: t('Neutron服务') },
        { key: 'glance_service', label: t('Glance服务') },
        { key: 'neutron_sserver_service', label: t('NeutronServer服务') },
        { key: 'keystone_service', label: t('Keystone服务') },
        { key: 'nova_service', label: t('Nova服务') },
        { key: 'cinder_service', label: t('Cinder服务') },
        { key: 'platform_ha_service', label: t('平台HA状态检测') },
      ]
    },
    guestOS: {
      title: t('GuestOS资源检测'),
      items: [
        { key: 'guestos_memory_usage_check', label: t('GuestOS内存利用率检测') },
        { key: 'guestos_cpu_usage_check', label: t('GuestOS CPU利用率检测') },
        { key: 'various_security_checks', label: t('卷状态') },
      ]
    },
    storage: {
      title: t('存储资源检测'),
      items: [
        { key: 'cluster_status', label: t('集群状态') },
        { key: 'storage_pool_check', label: t('存储池状态检测') },
        { key: 'storage_obj_status', label: t('存储对象状态') },
        { key: 'pg_status', label: t('PG状态') },
        { key: 'osd_status', label: t('OSD状态') },
      ]
    },
    networkResource: {
      title: t('网络资源检测'),
      items: [
        { key: 'virtual_router_status', label: t('虚拟路由状态') },
        { key: 'virtual_gateway_status', label: t('虚拟网关状态') },
        { key: 'port_status', label: t('端口状态') },
        { key: 'host_packet_loss_detection', label: t('主机丢包检测') },
        { key: 'response_content_health_check', label: t('基于响应内容的健康检查') },
      ]
    }
  });

  // 巡检状态
  const [isInspecting, setIsInspecting] = useState(false);
  const [inspectionProgress, setInspectionProgress] = useState(0);
  const [completedCount, setCompletedCount] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [startTime, setStartTime] = useState(null);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [currentInspectingItem, setCurrentInspectingItem] = useState(null);
  const [itemStatuses, setItemStatuses] = useState({});

  // 全局控制状态
  const [allCollapsed, setAllCollapsed] = useState(false);
  const [activeKeys, setActiveKeys] = useState(['hostOS', 'cloudPlatform', 'guestOS', 'storage', 'networkResource']);

  // 初始化项目状态
  useEffect(() => {
    // 初始化所有项目状态为等待中（显示转动特效）
    const initialStatuses = {};
    Object.keys(inspectionItems).forEach(category => {
      inspectionItems[category].items.forEach(item => {
        const itemId = `${category}_${item.key}`;
        initialStatuses[itemId] = 'waiting';
      });
    });
    setItemStatuses(initialStatuses);
  }, []);

  // 计时器效果
  useEffect(() => {
    let timer;
    if (isInspecting && startTime) {
      timer = setInterval(() => {
        setElapsedTime(Math.floor((Date.now() - startTime) / 1000));
      }, 1000);
    }
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [isInspecting, startTime]);



  // 处理收起/展开所有
  const handleToggleAll = () => {
    if (allCollapsed) {
      setActiveKeys(['hostOS', 'cloudPlatform', 'guestOS', 'storage', 'networkResource']);
      setAllCollapsed(false);
    } else {
      setActiveKeys([]);
      setAllCollapsed(true);
    }
  };

  // 处理折叠面板变化
  const handleCollapseChange = (keys) => {
    setActiveKeys(keys);
    setAllCollapsed(keys.length === 0);
  };



  // 格式化时间显示
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // 获取项目状态
  const getItemStatus = (category, itemKey) => {
    const itemId = `${category}_${itemKey}`;
    return itemStatuses[itemId] || 'waiting';
  };

  // 渲染状态图标
  const renderStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircleOutlined className={styles.statusCompleted} />;
      case 'running':
        return <LoadingOutlined className={styles.statusRunning} spin />;
      case 'waiting':
      default:
        return <LoadingOutlined className={styles.statusWaiting} spin />;
    }
  };

  // 开始巡检
  const handleStartInspection = () => {
    // 获取所有巡检项（现在不需要选择，全部执行）
    const allItems = [];
    Object.keys(inspectionItems).forEach(category => {
      inspectionItems[category].items.forEach(item => {
        allItems.push({
          category,
          key: item.key,
          label: item.label
        });
      });
    });

    // 初始化巡检状态
    setIsInspecting(true);
    setInspectionProgress(0);
    setCompletedCount(0);
    setTotalCount(allItems.length);
    setStartTime(Date.now());
    setElapsedTime(0);
    setCurrentInspectingItem(null);

    // 初始化所有项目状态为等待中（显示转动特效）
    const initialStatuses = {};
    allItems.forEach(item => {
      const itemId = `${item.category}_${item.key}`;
      initialStatuses[itemId] = 'waiting';
    });
    setItemStatuses(initialStatuses);

    // 开始逐个执行巡检
    executeInspectionSequence(allItems, 0);
    message.info(t('Inspection started, please wait...'));
  };

  // 执行巡检序列
  const executeInspectionSequence = (selectedItems, currentIndex) => {
    if (currentIndex >= selectedItems.length) {
      // 所有项目完成
      setIsInspecting(false);
      setCurrentInspectingItem(null);
      setInspectionProgress(100);
      message.success(t('Inspection completed successfully'));
      return;
    }

    const currentItem = selectedItems[currentIndex];
    const itemId = `${currentItem.category}_${currentItem.key}`;

    // 设置当前项目为进行中
    setCurrentInspectingItem(itemId);
    setItemStatuses(prev => ({
      ...prev,
      [itemId]: 'running'
    }));

    // 模拟巡检时间（1-3秒随机）
    const inspectionTime = Math.random() * 2000 + 1000;

    setTimeout(() => {
      // 完成当前项目
      setItemStatuses(prev => ({
        ...prev,
        [itemId]: 'completed'
      }));

      const newCompletedCount = currentIndex + 1;
      setCompletedCount(newCompletedCount);
      setInspectionProgress(Math.round((newCompletedCount / selectedItems.length) * 100));

      // 继续下一个项目
      executeInspectionSequence(selectedItems, currentIndex + 1);
    }, inspectionTime);
  };

  // 停止巡检
  const handleStopInspection = () => {
    setIsInspecting(false);
    setCurrentInspectingItem(null);
    setItemStatuses({});
    message.info(t('Inspection stopped'));
  };



  // 渲染巡检项目面板
  const renderInspectionPanel = (category, data) => {
    const { title, items } = data;

    return (
      <Panel
        header={
          <div className={styles.panelHeader}>
            <span className={styles.categoryTitleText}>
              {title}
            </span>
          </div>
        }
        key={category}
        className={styles.inspectionPanel}
      >
        <Row gutter={[16, 12]}>
          {items.map(item => {
            const status = getItemStatus(category, item.key);
            const itemClass = `${styles.inspectionItem} ${status ? styles[status] : ''}`;
            return (
              <Col xs={24} sm={12} md={8} lg={6} xl={6} key={item.key}>
                <div className={styles.inspectionItemWrapper}>
                  <div className={itemClass}>
                    <div className={styles.statusIcon}>
                      {renderStatusIcon(status)}
                    </div>
                    <span className={styles.itemLabel}>{item.label}</span>
                  </div>
                </div>
              </Col>
            );
          })}
        </Row>
      </Panel>
    );
  };

  return (
    <div className={styles.container}>
      {/* 页面头部 */}
      <div className={styles.header}>
        <div className={styles.titleSection}>
          <h1 className={styles.title}>
            <span className={styles.titleMain}>{t('New Health Inspection')}</span>
            <span className={styles.titleDescription}>{t('Configure platform-wide health inspection work items to ensure normal operation of various health indicators of the platform')}</span>
          </h1>
        </div>
        
        <div className={styles.actionButtons}>
          <div className={styles.leftButtons}>
            <Button
              type="primary"
              size="large"
              loading={isInspecting}
              onClick={handleStartInspection}
              disabled={isInspecting}
            >
              {isInspecting ? `${t('Inspecting')}... ${inspectionProgress}%` : t('Start Detection')}
            </Button>
          </div>
          <div className={styles.rightButtons}>
            <Space>
              <Button size="large">
                {t('Inspection Configuration')}
              </Button>
              <Button size="large">
                {t('Inspection Records')}
              </Button>
            </Space>
          </div>
        </div>
      </div>

      {/* 巡检进度显示 */}
      {isInspecting && (
        <div className={styles.progressContainer}>
          <div className={styles.progressHeader}>
            <div className={styles.progressInfo}>
              <div className={styles.progressText}>
                {t('Inspecting')}... {completedCount}/{totalCount} ({inspectionProgress}%)
              </div>
              <div className={styles.timeInfo}>
                {t('Elapsed Time')}: {formatTime(elapsedTime)}
              </div>
            </div>
            <Button
              danger
              onClick={handleStopInspection}
              className={styles.stopButton}
            >
              {t('Stop Detection')}
            </Button>
          </div>
          <Progress
            percent={inspectionProgress}
            status={inspectionProgress === 100 ? 'success' : 'active'}
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
            showInfo={true}
            strokeWidth={8}
            trailColor="#f0f0f0"
            format={(percent) => `${percent}%`}
          />
        </div>
      )}

      {/* 巡检项目配置 */}
      <div className={styles.content}>
        <div className={styles.inspectionConfig}>
          {/* 全局控制栏 */}
          <div className={styles.globalControls}>
            <div className={styles.globalTitle}>
              {t('Inspection Items')}
            </div>
            <div className={styles.globalButtons}>
              <Button
                size="small"
                onClick={handleToggleAll}
              >
                {allCollapsed ? t('Expand All') : t('Collapse All')}
              </Button>
            </div>
          </div>

          {/* 折叠面板 */}
          <Collapse
            activeKey={activeKeys}
            onChange={handleCollapseChange}
            expandIcon={({ isActive }) => (
              <CaretRightOutlined rotate={isActive ? 90 : 0} />
            )}
            className={styles.collapse}
          >
            {Object.keys(inspectionItems).map(category =>
              renderInspectionPanel(category, inspectionItems[category])
            )}
          </Collapse>
        </div>
      </div>
    </div>
  );
}
