import React, { useState, useEffect } from 'react';
import { Button, Tabs, Tag, Space, Card, Tree, Table } from 'antd';
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  WarningOutlined,
  FileTextOutlined,
  DownloadOutlined,
  PrinterOutlined,
  FolderOutlined,
  FileOutlined
} from '@ant-design/icons';
import styles from './index.less';

// 获取国际化函数
const t = (key) => {
  try {
    // 这里应该使用项目的国际化函数，暂时使用简单实现
    return key;
  } catch (error) {
    console.error('[HealthInspectionResult] 国际化函数错误:', error);
    return key; // 降级处理，返回原始key
  }
};

const { TabPane } = Tabs;

const HealthInspectionResult = () => {
  const [activeTab, setActiveTab] = useState('all');
  const [selectedItem, setSelectedItem] = useState(null);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [inspectionData, setInspectionData] = useState({
    totalItems: 32,
    normalItems: 28,
    warningItems: 2,
    errorItems: 2,
    startTime: '2024-07-22 16:30:45',
    endTime: '2024-07-22 16:31:30',
    duration: '00:00:45',
    results: [
      {
        id: 1,
        category: '存储资源巡检',
        name: '集群状态',
        status: 'error',
        message: 'UnknownHostException: null',
        details: '集群连接异常，请检查网络连接状态。',
        suggestion: '1. 检查网络连接是否正常\n2. 确认集群服务是否启动\n3. 验证防火墙设置\n4. 检查DNS解析是否正确'
      },
      {
        id: 2,
        category: '存储资源巡检',
        name: '存储状态安全检查',
        status: 'error',
        message: 'Connection timeout',
        details: '存储服务连接超时，请检查存储服务状态。',
        suggestion: '1. 检查存储服务是否正常运行\n2. 验证网络连接稳定性\n3. 调整连接超时参数\n4. 检查存储设备状态'
      },
      {
        id: 3,
        category: 'HostOS资源检测',
        name: 'CPU利用率检测',
        status: 'warning',
        message: 'CPU usage is 85%',
        details: 'CPU使用率较高，建议关注系统性能。',
        suggestion: '1. 检查高CPU占用的进程\n2. 优化系统配置\n3. 考虑扩容或负载均衡\n4. 定期清理系统垃圾'
      },
      {
        id: 4,
        category: 'HostOS资源检测',
        name: '内存利用率检测',
        status: 'warning',
        message: 'Memory usage is 78%',
        details: '内存使用率偏高，需要关注内存泄漏风险。',
        suggestion: '1. 检查内存占用较高的进程\n2. 优化应用程序内存使用\n3. 增加系统内存\n4. 配置合适的swap空间'
      },
      {
        id: 5,
        category: '云平台资源检测',
        name: '虚拟机状态检查',
        status: 'normal',
        message: 'All VMs are running normally',
        details: '所有虚拟机运行状态正常。',
        suggestion: '继续保持当前配置，定期监控虚拟机状态。'
      }
    ]
  });

  // 从sessionStorage获取巡检结果数据
  useEffect(() => {
    try {
      console.log('[HealthInspectionResult] 开始初始化组件数据');
      const storedData = sessionStorage.getItem('inspectionData');
      if (storedData) {
        const parsedData = JSON.parse(storedData);
        console.log('[HealthInspectionResult] 成功获取巡检数据:', parsedData);
        setInspectionData(parsedData);
        // 清除数据，避免刷新页面时重复加载
        sessionStorage.removeItem('inspectionData');
      } else {
        console.log('[HealthInspectionResult] 未找到存储的巡检数据，使用默认数据');
      }
      
      // 默认展开所有分类
      const categories = [...new Set(inspectionData.results.map(item => item.category))];
      setExpandedKeys(categories);
      console.log('[HealthInspectionResult] 设置展开的分类:', categories);
    } catch (error) {
      console.error('[HealthInspectionResult] 初始化数据时发生错误:', error);
      // 继续使用默认数据，不中断用户体验
    }
  }, []);

  // 状态图标映射
  const getStatusIcon = (status) => {
    try {
      switch (status) {
        case 'normal':
          return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
        case 'warning':
          return <WarningOutlined style={{ color: '#faad14' }} />;
        case 'error':
          return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
        default:
          console.warn('[HealthInspectionResult] 未知的状态类型:', status);
          return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      }
    } catch (error) {
      console.error('[HealthInspectionResult] 获取状态图标时发生错误:', error);
      return <CheckCircleOutlined style={{ color: '#52c41a' }} />; // 降级处理
    }
  };

  // 状态标签映射
  const getStatusTag = (status) => {
    try {
      switch (status) {
        case 'normal':
          return <Tag color="success">{t('Normal')}</Tag>;
        case 'warning':
          return <Tag color="warning">{t('Warning')}</Tag>;
        case 'error':
          return <Tag color="error">{t('Error')}</Tag>;
        default:
          console.warn('[HealthInspectionResult] 未知的状态类型:', status);
          return <Tag color="success">{t('Normal')}</Tag>;
      }
    } catch (error) {
      console.error('[HealthInspectionResult] 获取状态标签时发生错误:', error);
      return <Tag color="success">{t('Normal')}</Tag>; // 降级处理
    }
  };

  // 过滤数据 - 根据当前激活的Tab筛选数据
  const getFilteredData = () => {
    try {
      if (activeTab === 'all') {
        return inspectionData.results;
      }
      const filtered = inspectionData.results.filter(item => item.status === activeTab);
      console.log(`[HealthInspectionResult] 筛选${activeTab}状态的数据，共${filtered.length}条`);
      return filtered;
    } catch (error) {
      console.error('[HealthInspectionResult] 过滤数据时发生错误:', error);
      return []; // 降级处理，返回空数组
    }
  };

  // 构建树形数据 - 将巡检结果按类别组织成树形结构
  const getTreeData = () => {
    try {
      const filteredData = getFilteredData();
      const categoryMap = {};
      
      // 按类别分组
      filteredData.forEach(item => {
        if (!categoryMap[item.category]) {
          categoryMap[item.category] = [];
        }
        categoryMap[item.category].push(item);
      });

      // 构建树形结构
      const treeData = Object.keys(categoryMap).map(category => ({
        title: (
          <span className={styles.categoryTitle}>
            <FolderOutlined style={{ marginRight: 8, color: '#1890ff' }} />
            {category} ({categoryMap[category].length})
          </span>
        ),
        key: category,
        selectable: false,
        children: categoryMap[category].map(item => ({
          title: (
            <span className={styles.itemTitle}>
              {getStatusIcon(item.status)}
              <span style={{ marginLeft: 8 }}>{item.name}</span>
            </span>
          ),
          key: `${category}-${item.id}`,
          isLeaf: true,
          itemData: item
        }))
      }));
      
      console.log('[HealthInspectionResult] 构建树形数据完成，分类数量:', Object.keys(categoryMap).length);
      return treeData;
    } catch (error) {
      console.error('[HealthInspectionResult] 构建树形数据时发生错误:', error);
      return []; // 降级处理，返回空数组
    }
  };

  // 处理树节点选择 - 当用户点击巡检项时显示详情
  const handleTreeSelect = (keys, info) => {
    try {
      if (keys.length > 0 && info.node.isLeaf) {
        console.log('[HealthInspectionResult] 选择巡检项:', info.node.itemData?.name);
        setSelectedKeys(keys);
        setSelectedItem(info.node.itemData);
      }
    } catch (error) {
      console.error('[HealthInspectionResult] 处理树节点选择时发生错误:', error);
    }
  };

  // 处理树节点展开 - 记录用户展开的分类
  const handleTreeExpand = (keys) => {
    try {
      console.log('[HealthInspectionResult] 展开分类:', keys);
      setExpandedKeys(keys);
    } catch (error) {
      console.error('[HealthInspectionResult] 处理树节点展开时发生错误:', error);
    }
  };

  // 导出报告
  const handleExportReport = () => {
    try {
      console.log('[HealthInspectionResult] 用户点击导出报告');
      // TODO: 实现导出报告功能
    } catch (error) {
      console.error('[HealthInspectionResult] 导出报告时发生错误:', error);
    }
  };

  // 导出详情
  const handleExportDetails = () => {
    try {
      console.log('[HealthInspectionResult] 用户点击导出详情');
      // TODO: 实现导出详情功能
    } catch (error) {
      console.error('[HealthInspectionResult] 导出详情时发生错误:', error);
    }
  };

  // 返回巡检页面
  const handleBackToInspection = () => {
    try {
      console.log('[HealthInspectionResult] 用户返回巡检页面');
      window.location.href = '/base/new-health-inspection-admin';
    } catch (error) {
      console.error('[HealthInspectionResult] 返回巡检页面时发生错误:', error);
    }
  };

  return (
    <div className={styles.container}>
      {/* 页面头部 */}
      <div className={styles.header}>
        <div className={styles.titleSection}>
          <h1 className={styles.title}>
            检测结果：共{inspectionData.totalItems}项，其中{inspectionData.normalItems}项正常，
            {inspectionData.warningItems}项警告，{inspectionData.errorItems}项异常
          </h1>
          <div className={styles.subtitle}>
            检测时间：{inspectionData.startTime} - {inspectionData.endTime} 耗时：{inspectionData.duration}
          </div>
        </div>
        
        <div className={styles.actionButtons}>
          <Space>
            <Button icon={<FileTextOutlined />} onClick={handleExportReport}>
              {t('Export Report')}
            </Button>
            <Button icon={<DownloadOutlined />} onClick={handleExportDetails}>
              {t('Export Details')}
            </Button>
            <Button icon={<PrinterOutlined />}>
              {t('Send Log')}
            </Button>
          </Space>
        </div>
      </div>



      {/* 标签页 */}
      <Tabs activeKey={activeTab} onChange={setActiveTab} className={styles.resultTabs}>
        <TabPane
          tab={
            <span>
              <FileTextOutlined style={{ marginRight: 8, color: '#1890ff' }} />
              {t('All')}({inspectionData.totalItems})
            </span>
          }
          key="all"
        />
        <TabPane
          tab={
            <span>
              <CheckCircleOutlined style={{ marginRight: 8, color: '#52c41a' }} />
              {t('Normal')}({inspectionData.normalItems})
            </span>
          }
          key="normal"
        />
        <TabPane
          tab={
            <span>
              <WarningOutlined style={{ marginRight: 8, color: '#faad14' }} />
              {t('Warning')}({inspectionData.warningItems})
            </span>
          }
          key="warning"
        />
        <TabPane
          tab={
            <span>
              <CloseCircleOutlined style={{ marginRight: 8, color: '#ff4d4f' }} />
              {t('Error')}({inspectionData.errorItems})
            </span>
          }
          key="error"
        />
      </Tabs>

      {/* 主要内容区域 */}
      <div className={styles.content}>
        <div className={styles.leftPanel}>
          {/* 检测结果树形结构 */}
          <Card title={t('Detection Results')} className={styles.resultListCard}>
            <Tree
              className={styles.resultTree}
              treeData={getTreeData()}
              selectedKeys={selectedKeys}
              expandedKeys={expandedKeys}
              onSelect={handleTreeSelect}
              onExpand={handleTreeExpand}
              showIcon={false}
              blockNode
            />
          </Card>
        </div>

        <div className={styles.rightPanel}>
          {selectedItem ? (
            <div className={styles.detailContent}>
              {/* 检测结果详情表格 */}
              <Card title={t('Detection Result Details')} className={styles.detailCard}>
                <Table
                  className={styles.detailTable}
                  dataSource={[
                    {
                      key: 'name',
                      field: t('Detection Item'),
                      value: selectedItem.name
                    },
                    {
                      key: 'category',
                      field: t('Category'),
                      value: selectedItem.category
                    },
                    {
                      key: 'status',
                      field: t('Status'),
                      value: getStatusTag(selectedItem.status)
                    },
                    {
                      key: 'message',
                      field: t('Detection Result'),
                      value: selectedItem.message
                    },
                    {
                      key: 'details',
                      field: t('Detailed Description'),
                      value: selectedItem.details
                    }
                  ]}
                  columns={[
                    {
                      title: t('Field'),
                      dataIndex: 'field',
                      key: 'field',
                      width: 150,
                      className: styles.fieldColumn
                    },
                    {
                      title: t('Value'),
                      dataIndex: 'value',
                      key: 'value',
                      render: (value) => (
                        <div className={styles.valueColumn}>
                          {typeof value === 'string' ? value : value}
                        </div>
                      )
                    }
                  ]}
                  pagination={false}
                  showHeader={true}
                  size="middle"
                />
              </Card>

              {/* 修复建议表格 */}
              <Card title={t('Repair Suggestions')} className={styles.suggestionCard}>
                <Table
                  className={styles.suggestionTable}
                  dataSource={selectedItem.suggestion.split('\n').map((line, index) => ({
                    key: index,
                    step: index + 1,
                    suggestion: line
                  }))}
                  columns={[
                    {
                      title: t('Step'),
                      dataIndex: 'step',
                      key: 'step',
                      width: 60,
                      align: 'center',
                      render: (step) => (
                        <div className={styles.stepNumber}>{step}</div>
                      )
                    },
                    {
                      title: t('Suggestion'),
                      dataIndex: 'suggestion',
                      key: 'suggestion',
                      render: (suggestion) => (
                        <div className={styles.suggestionText}>{suggestion}</div>
                      )
                    }
                  ]}
                  pagination={false}
                  showHeader={true}
                  size="middle"
                />
              </Card>
            </div>
          ) : (
            <div className={styles.emptyState}>
              <div className={styles.emptyIcon}>
                <FileTextOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />
              </div>
              <div className={styles.emptyText}>{t('Please select a detection item from the left to view details')}</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default HealthInspectionResult;
