.container {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

// 页面头部样式
.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.titleSection {
  flex: 1;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.subtitle {
  font-size: 14px;
  color: #8c8c8c;
  margin: 0;
}

.actionButtons {
  flex-shrink: 0;
  margin-left: 24px;
}



// 主要内容区域
.content {
  display: flex;
  gap: 24px;
  height: calc(100vh - 300px);
}

.leftPanel {
  width: 280px;
  flex-shrink: 0;
}

.rightPanel {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
  display: flex;
  flex-direction: column;
}

// 结果列表卡片样式
.resultListCard {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 100%;

  :global(.ant-card-head) {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 24px;
  }

  :global(.ant-card-head-title) {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
  }

  :global(.ant-card-body) {
    padding: 0;
    height: calc(100% - 57px);
    overflow: hidden;
  }
}

.resultList {
  height: 100%;
  overflow-y: auto;
}

.resultItem {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    background: #f5f5f5;
  }

  &.selected {
    background: #e6f7ff;
    border-left: 3px solid #1890ff;
  }

  &:last-child {
    border-bottom: none;
  }
}

.resultItemHeader {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.resultItemIcon {
  margin-right: 8px;
  font-size: 16px;
}

.resultItemName {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  flex: 1;
}

.resultItemCategory {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.resultItemMessage {
  font-size: 13px;
  color: #595959;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

// 标签页样式
.resultTabs {
  margin-bottom: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0 24px;

  :global(.ant-tabs-nav) {
    margin-bottom: 0;
  }

  :global(.ant-tabs-tab) {
    font-size: 14px;
    padding: 16px 20px;
  }

  :global(.ant-tabs-content-holder) {
    display: none;
  }
}

// 详情内容样式
.detailContent {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detailCard {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  :global(.ant-card-head) {
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    padding: 12px 16px;
  }

  :global(.ant-card-head-title) {
    font-size: 14px;
    font-weight: 600;
    color: #262626;
  }

  :global(.ant-card-body) {
    padding: 0;
  }
}

// 详情表格样式
.detailTable {
  :global(.ant-table-thead) {
    :global(.ant-table-cell) {
      background: #fafafa;
      font-weight: 600;
      color: #262626;
      border-bottom: 2px solid #f0f0f0;
    }
  }

  :global(.ant-table-tbody) {
    :global(.ant-table-cell) {
      border-bottom: 1px solid #f0f0f0;
      padding: 16px;
      vertical-align: top;
    }
  }
}

.fieldColumn {
  font-weight: 600;
  color: #262626;
  background: #fafafa;
}

.valueColumn {
  color: #595959;
  line-height: 1.6;
  word-break: break-all;
}

// 空状态样式
.emptyState {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.emptyIcon {
  margin-bottom: 16px;
}

.emptyText {
  font-size: 14px;
  color: #8c8c8c;
}

// 修复建议卡片
.suggestionCard {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  flex: 1;

  :global(.ant-card-head) {
    background: #fff7e6;
    border-bottom: 1px solid #ffd591;
    padding: 12px 16px;
  }

  :global(.ant-card-head-title) {
    font-size: 14px;
    font-weight: 600;
    color: #d46b08;
  }

  :global(.ant-card-body) {
    padding: 0;
  }
}

// 修复建议表格样式
.suggestionTable {
  :global(.ant-table-thead) {
    :global(.ant-table-cell) {
      background: #fff7e6;
      font-weight: 600;
      color: #d46b08;
      border-bottom: 2px solid #ffd591;
    }
  }

  :global(.ant-table-tbody) {
    :global(.ant-table-cell) {
      border-bottom: 1px solid #ffd591;
      padding: 12px 16px;
      vertical-align: top;
    }
  }
}

.stepNumber {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #d46b08;
  color: white;
  font-weight: 600;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.suggestionText {
  color: #595959;
  line-height: 1.6;
}

// 响应式设计
@media (max-width: 1200px) {
  .content {
    flex-direction: column;
    height: auto;
  }
  
  .leftPanel {
    width: 100%;
  }
  
  .rightPanel {
    min-height: 400px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .header {
    flex-direction: column;
    gap: 16px;
    padding: 16px;
  }
  
  .actionButtons {
    margin-left: 0;
    width: 100%;
  }
  
  .statisticsRow {
    :global(.ant-col) {
      margin-bottom: 16px;
    }
  }
}
