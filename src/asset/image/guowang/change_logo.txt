1.scp cloud-logo.png favicon.ico logo.png三个文件到skyline_console所在节点
2. docker cp cloud-logo.png skyline_console:/var/lib/kolla/venv/lib/python3.8/site-packages/skyline_console/static/asset/image/cloud-logo.png
docker cp logo.png skyline_console:/var/lib/kolla/venv/lib/python3.8/site-packages/skyline_console/static/asset/image/logo.png
docker cp favicon.ico skyline_console:/var/lib/kolla/venv/lib/python3.8/site-packages/skyline_console/static/favicon.ico

进入容器skyline_console:
docker exec -itu root skyline_console bash

然后执行:
cd /var/lib/kolla/venv/lib/python3.8/site-packages/skyline_console/static
vi main.bundle.xxx.js 其中main.bundle.xxx.js 为实际文件的名字
搜索以下所有字词: 关闭公网网关        把后面相邻的'云'改为: 配电网云平台
搜索以下所有字词: UStack有栈后台管理系统    将它改为: 配电网信息基础设施安全弹性云化服务平台

保存vi退出
执行: gzip -c main.bundle.xxx.js > main.bundle.xxx.js.gz   其中main.bundle.xxx.js 为实际文件的名字，注意修改