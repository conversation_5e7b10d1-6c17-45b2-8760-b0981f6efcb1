{"3600": "3600", " You can go to the console to ": " You can go to the console to ", "\"Shared\" volume can be mounted on multiple instances": "\"Shared\" volume can be mounted on multiple instances", "\"v=spf1 ipv4=********* include:examplesender.email +all\" <ul><li><b>v=spf1:</b> Tells the server that this contains an SPF record. Every SPF record must begin with this string.</li> <li><b>Guest List:</b> Then comes the “guest list” portion of the SPF record or the list of authorized IP addresses. In this example, the SPF record is telling the server that ipv4=********* is authorized to send emails on behalf of the domain.</li> <li><b>include:examplesender.net:</b> is an example of the include tag, which tells the server what third-party organizations are authorized to send emails on behalf of the domain. This tag signals that the content of the SPF record for the included domain (examplesender.net) should be checked and the IP addresses it contains should also be considered authorized. Multiple domains can be included within an SPF record but this tag will only work for valid domains.</li><li><b>-all:</b> Tells, the server that addresses not listed in the SPF record are not authorized to send emails and should be rejected.</li></ul>": "\"v=spf1 ipv4=********* include:examplesender.email +all\" <ul><li><b>v=spf1:</b> Tells the server that this contains an SPF record. Every SPF record must begin with this string.</li> <li><b>Guest List:</b> Then comes the “guest list” portion of the SPF record or the list of authorized IP addresses. In this example, the SPF record is telling the server that ipv4=********* is authorized to send emails on behalf of the domain.</li> <li><b>include:examplesender.net:</b> is an example of the include tag, which tells the server what third-party organizations are authorized to send emails on behalf of the domain. This tag signals that the content of the SPF record for the included domain (examplesender.net) should be checked and the IP addresses it contains should also be considered authorized. Multiple domains can be included within an SPF record but this tag will only work for valid domains.</li><li><b>-all:</b> Tells, the server that addresses not listed in the SPF record are not authorized to send emails and should be rejected.</li></ul>", "'ip' rule represents IPv4 or IPv6 address, 'cert' rule represents TLS certificate, 'user' rule represents username or usergroup, 'cephx' rule represents ceph auth ID.": "'ip' rule represents IPv4 or IPv6 address, 'cert' rule represents TLS certificate, 'user' rule represents username or usergroup, 'cephx' rule represents ceph auth ID.", "-1 means no connection limit": "-1 means no connection limit", ".": ".", "0 iodef mailto:<EMAIL> <ul><li><b>0:</b> is flag. An unsigned integer between 0-255.</li> <li><b>iodef:</b> An ASCII string that represents the identifier of the property represented by the record.<br />Available Tags: \"issue\", \"issuewild\", \"iodef\"</li><li><b>mailto:<EMAIL>:</b> The value associated with the tag.</li></ul>": "0 iodef mailto:<EMAIL> <ul><li><b>0:</b> is flag. An unsigned integer between 0-255.</li> <li><b>iodef:</b> An ASCII string that represents the identifier of the property represented by the record.<br />Available Tags: \"issue\", \"issuewild\", \"iodef\"</li><li><b>mailto:<EMAIL>:</b> The value associated with the tag.</li></ul>", "1. The backup can only capture the data that has been written to the volume at the beginning of the backup task, excluding the data in the cache at that time.": "1. The backup can only capture the data that has been written to the volume at the beginning of the backup task, excluding the data in the cache at that time.", "1. The name of the custom resource class property should start with CUSTOM_, can only contain uppercase letters A ~ Z, numbers 0 ~ 9 or underscores, and the length should not exceed 255 characters (for example: CUSTOM_BAREMETAL_SMALL).": "1. The name of the custom resource class property should start with CUSTOM_, can only contain uppercase letters A ~ Z, numbers 0 ~ 9 or underscores, and the length should not exceed 255 characters (for example: CUSTOM_BAREMETAL_SMALL).", "1. The name of the trait should start with CUSTOM_, can only contain uppercase letters A ~ Z, numbers 0 ~ 9 or underscores, and the length should not exceed 255 characters (for example: CUSTOM_TRAIT1).": "1. The name of the trait should start with CUSTOM_, can only contain uppercase letters A ~ Z, numbers 0 ~ 9 or underscores, and the length should not exceed 255 characters (for example: CUSTOM_TRAIT1).", "1. The volume associated with the backup is available.": "1. The volume associated with the backup is available.", "1. You can create {resources} using ports or port ranges.": "1. You can create {resources} using ports or port ranges.", "10 0 5060 server1.example.com. <ul><li>\"10\" is the priority of the record. The lower the value, the higher the priority.</li><li>0 is the weight of the record. This is the weight of which this record has a chance to be used when there are multiple matching SRV records of the same priority.</li><li>5060 is the port of the record. This specifies the port on which the application or service is running.</li> <li>server1.example.com is the target of the record. This specifies the domain of the application or service the record is for. SRV records must specify a target which is either an A record or AAAA record, and may not use CNAME records.</li></ul>": "10 0 5060 server1.example.com. <ul><li>\"10\" is the priority of the record. The lower the value, the higher the priority.</li><li>0 is the weight of the record. This is the weight of which this record has a chance to be used when there are multiple matching SRV records of the same priority.</li><li>5060 is the port of the record. This specifies the port on which the application or service is running.</li> <li>server1.example.com is the target of the record. This specifies the domain of the application or service the record is for. SRV records must specify a target which is either an A record or AAAA record, and may not use CNAME records.</li></ul>", "10 mail.example.com <ul><li><b>10:</b> Priority</li> <li><b>mail.example.com:</b> Value</li></ul>": "10 mail.example.com <ul><li><b>10:</b> Priority</li> <li><b>mail.example.com:</b> Value</li></ul>", "10s": "10s", "1D": "1D", "1H": "1H", "1min": "1min", "2. In the same protocol, you cannot create multiple {resources} for the same source port or source port range.": "2. In the same protocol, you cannot create multiple {resources} for the same source port or source port range.", "2. The trait of the scheduled node needs to correspond to the trait of the flavor used by the ironic instance; by injecting the necessary traits into the ironic instance, the computing service will only schedule the instance to the bare metal node with all the necessary traits (for example, the ironic instance which use the flavor that has CUSTOM_TRAIT1 as a necessary trait, can be scheduled to the node which has the trait of CUSTOM_TRAIT1).": "2. The trait of the scheduled node needs to correspond to the trait of the flavor used by the ironic instance; by injecting the necessary traits into the ironic instance, the computing service will only schedule the instance to the bare metal node with all the necessary traits (for example, the ironic instance which use the flavor that has CUSTOM_TRAIT1 as a necessary trait, can be scheduled to the node which has the trait of CUSTOM_TRAIT1).", "2. The volume associated with the backup has been mounted, and the instance is shut down.": "2. The volume associated with the backup has been mounted, and the instance is shut down.", "2. To ensure the integrity of the data, it is recommended that you suspend the write operation of all files when creating a backup.": "2. To ensure the integrity of the data, it is recommended that you suspend the write operation of all files when creating a backup.", "2. You can customize the resource class name of the flavor, but it needs to correspond to the resource class of the scheduled node (for example, the resource class name of the scheduling node is baremetal.with-GPU, and the custom resource class name of the flavor is CUSTOM_BAREMETAL_WITH_GPU=1).": "2. You can customize the resource class name of the flavor, but it needs to correspond to the resource class of the scheduled node (for example, the resource class name of the scheduling node is baremetal.with-GPU, and the custom resource class name of the flavor is CUSTOM_BAREMETAL_WITH_GPU=1).", "3. When using a port range to create a port mapping, the size of the external port range is required to be the same as the size of the internal port range. For example, the external port range is 80:90 and the internal port range is 8080:8090.": "3. When using a port range to create a port mapping, the size of the external port range is required to be the same as the size of the internal port range. For example, the external port range is 80:90 and the internal port range is 8080:8090.", "4 2 123456789abcdef67890123456789abcdef67890123456789abcdef123456789 <ul> <li><b>4 is Algorithm:</b> Algorithm (0: reserved; 1: RSA; 2: DSA, 3: ECDSA; 4: Ed25519; 6:Ed448)</li> <li><b>2 is Type:</b> Algorithm used to hash the public key (0: reserved; 1: SHA-1; 2: SHA-256)</li> <li><b>Last parameter is Fingerprint:</b> Hexadecimal representation of the hash result, as text</li> </ul>": "4 2 123456789abcdef67890123456789abcdef67890123456789abcdef123456789 <ul> <li><b>4 is Algorithm:</b> Algorithm (0: reserved; 1: RSA; 2: DSA, 3: ECDS<PERSON>; 4: Ed25519; 6:Ed448)</li> <li><b>2 is Type:</b> Algorithm used to hash the public key (0: reserved; 1: SHA-1; 2: SHA-256)</li> <li><b>Last parameter is Fingerprint:</b> Hexadecimal representation of the hash result, as text</li> </ul>", "4. When you use a port range to create {resources}, multiple {resources} will be created in batches. ": "4. When you use a port range to create {resources}, multiple {resources} will be created in batches. ", "5min": "5min", "8 to 16 characters, at least one uppercase letter, one lowercase letter, one number.": "8 to 16 characters, at least one uppercase letter, one lowercase letter, one number.", "8 to 32 characters, at least one uppercase letter, one lowercase letter, one number and one special character.": "8 to 32 characters, at least one uppercase letter, one lowercase letter, one number and one special character.", "<username> or <username>@<domain>": "<username> or <username>@<domain>", "A command that will be sent to the container": "A command that will be sent to the container", "A container with the same name already exists": "A container with the same name already exists", "A dynamic scheduling algorithm that estimates the server load based on the number of currently active connections. The system allocates new connection requests to the server with the least number of current connections. Commonly used for long connection services, such as database connections and other services.": "A dynamic scheduling algorithm that estimates the server load based on the number of currently active connections. The system allocates new connection requests to the server with the least number of current connections. Commonly used for long connection services, such as database connections and other services.", "A host aggregate can be associated with at most one AZ. Once the association is established, the AZ cannot be disassociated.": "A host aggregate can be associated with at most one AZ. Once the association is established, the AZ cannot be disassociated.", "A public container will allow anyone to use the objects in your container through a public URL.": "A public container will allow anyone to use the objects in your container through a public URL.", "A rule specified before insertion or after insertion a rule. If both are not specified, the new rule is inserted as the first rule of the policy.": "A rule specified before insertion or after insertion a rule. If both are not specified, the new rule is inserted as the first rule of the policy.", "A snapshot is an image which preserves the disk state of a running instance, which can be used to start a new instance.": "A snapshot is an image which preserves the disk state of a running instance, which can be used to start a new instance.", "A template is a YAML file that contains configuration information, please enter the correct format.": "A template is a YAML file that contains configuration information, please enter the correct format.", "A template is a YAML file that contains configuration information.": "A template is a YAML file that contains configuration information.", "ADMINISTRATOR": "ADMINISTRATOR", "ADOPT COMPLETE": "ADOPT COMPLETE", "AH": "AH", "AKI - Amazon kernel image format": "AKI - Amazon kernel image format", "ALLOW": "ALLOW", "AMI - Amazon server image format": "AMI - Amazon server image format", "ANY": "ANY", "API Address": "API Address", "ARI - Amazon ramdisk image format": "ARI - Amazon ramdisk image format", "ARM Architecture": "ARM Architecture", "Abandon Stack": "Abandon Stack", "Abandoning this stack will preserve the resources deployed by the stack.": "Abandoning this stack will preserve the resources deployed by the stack.", "Abort Upload": "Abort Upload", "Accept Volume Transfer": "Accept Volume Transfer", "Access Control": "Access Control", "Access Key": "Access Key", "Access Level": "Access Level", "Access Rules": "Access Rules", "Access Rules Status": "Access Rules Status", "Access To": "Access To", "Access Type": "Access Type", "Access Type Setting": "Access Type Setting", "Action": "Action", "Action Logs": "Action Logs", "Active": "Active", "Active Status": "Active Status", "Add": "Add", "Add Access Rule": "Add Access Rule", "Add Custom Metadata": "Add Custom Metadata", "Add Data Disks": "Add Data Disks", "Add Environment Variable": "Add Environment Variable", "Add Exposed Ports": "Add Exposed Ports", "Add External Members": "Add External Members", "Add Extra Info": "Add Extra Info", "Add Extra Spec": "Add Extra Spec", "Add Host": "Add Host", "Add IP": "Add IP", "Add Label": "Add Label", "Add Member": "Add Member", "Add Metadata": "Add Metada<PERSON>", "Add NUMA Node": "Add NUMA Node", "Add Network": "Add Network", "Add Policy": "Add Policy", "Add Property": "Add Property", "Add Router": "Add Router", "Add Virtual LAN": "Add Virtual LAN", "Add hosts to the aggregate or remove hosts from it. Hosts can be in multiple aggregates.": "Add hosts to the aggregate or remove hosts from it. Hosts can be in multiple aggregates.", "Add network": "Add network", "Add scheduler hints": "Add scheduler hints", "Additional Labels": "Additional Labels", "Additional routes announced to the instance, one entry per line(e.g. *************/24,***********)": "Additional routes announced to the instance, one entry per line(e.g. *************/24,***********)", "Additional routes announced to the instance, one entry per line(e.g. {ip})": "Additional routes announced to the instance, one entry per line(e.g. {ip})", "Address": "Address", "Address Record": "Address Record", "Addresses": "Addresses", "Admin State": "Admin State", "Admin State Up": "Admin State Up", "Admin Status": "Admin Status", "Administrator": "Administrator", "Adopt Complete": "Adopt Complete", "Adopt Failed": "Adopt Failed", "Adopt In Progress": "Adopt In Progress", "Advanced": "Advanced", "Advanced Options": "Advanced Options", "Advanced Params": "Advanced Params", "Affiliated Domain": "Affiliated Domain", "Affiliated Domain ID/Name": "Affiliated Domain ID/Name", "Affinity": "Affinity", "Affinity (mandatory):": "Affinity (mandatory):", "Affinity (not mandatory):": "Affinity (not mandatory):", "Afghanistan": "Afghanistan", "After attaching interface, you may need to login the instance to update the network interface configuration and restart the network service.": "After attaching interface, you may need to login the instance to update the network interface configuration and restart the network service.", "After disable the compute service, the new instance will not schedule to the compute node.": "After disable the compute service, the new instance will not schedule to the compute node.", "After shelving, the instance will be shut down, resources will be released, and the snapshot will be saved to Glance. This will take about a few minutes, please be patient. You also can choose to unshelve to restore the instance.": "After shelving, the instance will be shut down, resources will be released, and the snapshot will be saved to Glance. This will take about a few minutes, please be patient. You also can choose to unshelve to restore the instance.", "After the share is expanded, the share cannot be reduced.": "After the share is expanded, the share cannot be reduced.", "After the volume is expanded, the volume cannot be reduced.": "After the volume is expanded, the volume cannot be reduced.", "Agent": "Agent", "Agree to force shutdown": "Agree to force shutdown", "Albania": "Albania", "Algeria": "Algeria", "All": "All", "All Flavors": "All Flavors", "All ICMP": "All ICMP", "All Images": "All Images", "All Networks": "All Networks", "All Port": "All Port", "All Proto": "All Proto", "All QoS Policies": "All QoS Policies", "All TCP": "All TCP", "All UDP": "All UDP", "All data downloaded.": "All data downloaded.", "All network segments are indicated by \"*\", not \"0.0.0.0/0\"": "All network segments are indicated by \"*\", not \"0.0.0.0/0\"", "Allocate IP": "Allocate IP", "Allocation Pools": "Allocation Pools", "Allowed Address Pairs": "Allowed Address Pairs", "Allowed Host": "Allowed Host", "Always": "Always", "American Samoa": "American Samoa", "An object with the same name already exists": "An object with the same name already exists", "Andorra": "Andorra", "Angola": "Angola", "Anguilla": "<PERSON><PERSON><PERSON>", "Anti-Affinity": "Anti-Affinity", "Anti-affinity (mandatory):": "Anti-affinity (mandatory):", "Anti-affinity (not mandatory):": "Anti-affinity (not mandatory):", "Antigua and Barbuda": "Antigua and Barbuda", "Any": "Any", "Any(Random)": "Any(Random)", "Application Credentials": "Application Credentials", "Application Template": "Application Template", "Apply Latency(ms)": "Apply Latency(ms)", "Applying": "Applying", "Arch": "Arch", "Architecture": "Architecture", "Are you sure set the project { project } as the default project? User login is automatically logged into the default project.": "Are you sure set the project { project } as the default project? User login is automatically logged into the default project.", "Are you sure to cancel transfer volume { name }? ": "Are you sure to cancel transfer volume { name }? ", "Are you sure to delete instance { name }? ": "Are you sure to delete instance { name }? ", "Are you sure to delete volume { name }? ": "Are you sure to delete volume { name }? ", "Are you sure to download data?": "Are you sure to download data?", "Are you sure to forbidden domain { name }? Forbidden the domain will have negative effect, and users associated with the domain will not be able to log in if they are only assigned to the domain": "Are you sure to forbidden domain { name }? Forbidden the domain will have negative effect, and users associated with the domain will not be able to log in if they are only assigned to the domain", "Are you sure to forbidden project { name }? Forbidden the project will have negative effect, and users associated with the project will not be able to log in if they are only assigned to the project": "Are you sure to forbidden project { name }? Forbidden the project will have negative effect, and users associated with the project will not be able to log in if they are only assigned to the project", "Are you sure to forbidden user { name }? Forbidden the user will not allow login in ": "Are you sure to forbidden user { name }? Forbidden the user will not allow login in ", "Are you sure to jump directly to the console? The console will open in a new page later.": "Are you sure to jump directly to the console? The console will open in a new page later.", "Are you sure to remove the default project?": "Are you sure to remove the default project?", "Are you sure to shelve instance { name }? ": "Are you sure to shelve instance { name }? ", "Are you sure to { action } {name}?": "Are you sure to { action } {name}?", "Are you sure to {action} (Host: {name})?": "Are you sure to {action} (Host: {name})?", "Are you sure to {action} (Segment: {name})?": "Are you sure to {action} (Segment: {name})?", "Are you sure to {action} (instance: {name})?": "Are you sure to {action} (instance: {name})?", "Are you sure to {action}?": "Are you sure to {action}?", "Are you sure to {action}? (Record Set: {name} - {id})": "Are you sure to {action}? (Record Set: {name} - {id})", "Are you sure to {action}? (Zone: {name})": "Are you sure to {action}? (Zone: {name})", "Argentina": "Argentina", "Armenia": "Armenia", "Aruba": "Aruba", "Associate": "Associate", "Associate Floating IP": "Associate Floating IP", "Associate IP": "Associate IP", "Associate Network": "Associate Network", "Associated Ports": "Associated Ports", "Associated QoS Spec ID": "Associated QoS Spec ID", "Associated QoS Spec ID/Name": "Associated QoS Spec ID/Name", "Associated Resource": "Associated Resource", "Associated Resource Types": "Associated Resource Types", "Associated Resources": "Associated Resources", "Associations": "Associations", "Attach": "Attach", "Attach Instance": "Attach Instance", "Attach Interface": "Attach Interface", "Attach Network": "Attach Network", "Attach Security Group": "Attach Security Group", "Attach USB": "Attach USB", "Attach Volume": "Attach Volume", "Attach volume": "Attach volume", "Attached Device": "Attached Device", "Attached To": "Attached To", "Attaching": "Attaching", "Attachments Info": "Attachments Info", "Attributes": "Attributes", "Audited": "Audited", "Australia": "Australia", "Austria": "Austria", "Auth Algorithm": "Auth Algorithm", "Auth Key": "Auth Key", "Auto": "Auto", "Auto Healing": "Auto Healing", "Auto Inspect": "Auto Inspect", "Auto Scaling": "Auto Scaling", "Auto allocate mac address": "Auto allocate mac address", "Auto scaling feature will be enabled": "Auto scaling feature will be enabled", "Automatically Assigned Address": "Automatically Assigned Address", "Automatically repair unhealhty nodes": "Automatically repair unhealhty nodes", "Availability Zone": "Availability Zone", "Availability Zone Hints": "Availability Zone Hints", "Availability Zone Info": "Availability Zone Info", "Availability Zone Name": "Availability Zone Name", "Availability Zones": "Availability Zones", "Availability zone refers to a physical area where power and network are independent of each other in the same area. In the same region, the availability zone and the availability zone can communicate with each other in the intranet, and the available zones can achieve fault isolation.": "Availability zone refers to a physical area where power and network are independent of each other in the same area. In the same region, the availability zone and the availability zone can communicate with each other in the intranet, and the available zones can achieve fault isolation.", "Available": "Available", "Available Zone": "Available Zone", "Average PGs per OSD": "Average PGs per OSD", "Awaiting Transfer": "Awaiting Transfer", "Azerbaijan": "Azerbaijan", "BLOCK I/O(B)": "BLOCK I/O(B)", "Back": "Back", "Back End": "Back End", "Back to Home": "Back to Home", "Back to login page": "Back to login page", "Backend": "Backend", "Backend Name": "Backend Name", "Backing Up": "Backing Up", "Backup": "Backup", "Backup Detail": "Backup Detail", "Backup File": "Backup File", "Backup File Location": "Backup File Location", "Backup Mode": "Backup Mode", "Backups": "Backups", "Backups & Snapshots": "Backups & Snapshots", "Bad Gateway (code: 502) ": "Bad Gateway (code: 502) ", "Bahamas": "Bahamas", "Bahrain": "Bahrain", "BandWidth Limit Egress": "BandWidth Limit Egress", "BandWidth Limit Ingress": "BandWidth Limit Ingress", "Bandwidth limit": "Bandwidth limit", "Bangladesh": "Bangladesh", "Barbados": "Barbados", "Bare Metal": "Bare Metal", "Bare Metal Enroll": "Bare Metal Enroll", "Bare Metal Node Detail": "Bare Metal Node Detail", "Bare Metal Nodes": "Bare Metal Nodes", "BareMetal Parameters": "BareMetal Parameters", "Base Config": "Base Config", "Base Info": "Base Info", "Basic Parameters": "Basic Parameters", "Batch Allocate": "<PERSON>ch Allocate", "Before deleting the project, it is recommended to clean up the resources under the project.": "Before deleting the project, it is recommended to clean up the resources under the project.", "Belarus": "Belarus", "Belgium": "Belgium", "Belize": "Belize", "Benin": "Benin", "Bermuda": "Bermuda", "Bhutan": "Bhutan", "Big Data": "Big Data", "Bind Device": "Bind Device", "Bind Device Type": "Bind Device Type", "Bind Resource": "Bind Resource", "Bind Resource Name": "Bind Resource Name", "Binding": "Binding", "Binding Groups": "Binding Groups", "Binding Instance": "Binding Instance", "Binding Profile": "Binding Profile", "Binding Users": "Binding Users", "Blank Volume": "Blank Volume", "Block Device Mapping": "Block Device Mapping", "Block Migrate": "Block Migrate", "Block Storage Services": "Block Storage Services", "Blocked": "Blocked", "Bolivia": "Bolivia", "Boot Device": "Boot Device", "Boot From Volume": "Boot From Volume", "Boot Interface": "Boot Interface", "Boot Mode of BIOS": "Boot Mode of BIOS", "Bootable": "Bootable", "Bootable Volume": "Bootable Volume", "Bosnia and Herzegovina": "Bosnia and Herzegovina", "Both of Frontend and Backend": "Both of Frontend and Backend", "Botswana": "Botswana", "Brazil": "Brazil", "British Indian Ocean Territory": "British Indian Ocean Territory", "Brunei Darussalam": "Brunei Darussalam", "Build": "Build", "Building": "Building", "Bulgaria": "Bulgaria", "Burkina Faso": "Burkina Faso", "Burst limit": "Burst limit", "Burundi": "Burundi", "By default, for security reasons, application credentials are forbidden from being used for creating or destructing additional application credentials or keystone trusts. If your application credential needs to be able to perform these actions, check unrestricted.": "By default, for security reasons, application credentials are forbidden from being used for creating or destructing additional application credentials or keystone trusts. If your application credential needs to be able to perform these actions, check unrestricted.", "CA Certificate": "CA Certificate", "CA Certificates": "CA Certificates", "CHECK COMPLETE": "CHECK COMPLETE", "CIDR": "CIDR", "CIDR Format Error(e.g. ***********/24, 2001:DB8::/48)": "CIDR Format Error(e.g. ***********/24, 2001:DB8::/48)", "CIFS": "CIFS", "CMD": "CMD", "COE": "COE", "COE Version": "COE Version", "CPU": "CPU", "CPU %": "CPU %", "CPU (Core)": "CPU (Core)", "CPU Arch": "CPU Arch", "CPU Cores": "CPU Cores", "CPU Policy": "CPU Policy", "CPU Thread Policy": "CPU Thread Policy", "CPU Usage(%)": "CPU Usage(%)", "CPU Usages (Core)": "CPU Usages (Core)", "CPU value is { cpu }, NUMA CPU value is { totalCpu }, need to be equal. ": "CPU value is { cpu }, NUMA CPU value is { totalCpu }, need to be equal. ", "CPU(Core)": "CPU(Core)", "CREATE COMPLETE": "CREATE COMPLETE", "CREATE FAILED": "CREATE FAILED", "CREATE IN PROGRESS": "CREATE IN PROGRESS", "Cache Service": "Cache Service", "Cameroon": "Cameroon", "Can add { number } {name}": "Can add { number } {name}", "Canada": "Canada", "Cancel": "Cancel", "Cancel Download": "Cancel Download", "Cancel Select": "Cancel Select", "Cancel Transfer": "Cancel Transfer", "Cancel download successfully.": "Cancel download successfully.", "Cancel upload successfully.": "Cancel upload successfully.", "Canonical Name Record": "Canonical Name Record", "Capacity & Type": "Capacity & Type", "Capacity (GiB)": "Capacity (GiB)", "Cape Verde": "Cape Verde", "Capsule Detail": "Capsule Detail", "Capsule Type": "Capsule Type", "Capsules": "Capsules", "Cascading deletion": "Cascading deletion", "Cast Rules To Read Only": "Cast Rules To Read Only", "Category": "Category", "Cayman Islands": "Cayman Islands", "CentOS": "CentOS", "Central African Republic": "Central African Republic", "CephFS": "CephFS", "Cephx": "Cephx", "Cert": "Cert", "Certificate Authority Authorization Record": "Certificate Authority Authorization Record", "Certificate Content": "Certificate Content", "Certificate Detail": "Certificate Detail", "Certificate Name": "Certificate Name", "Certificate Type": "Certificate Type", "Certificates": "Certificates", "Chad": "Chad", "Change Password": "Change Password", "Change Type": "Change Type", "Change password": "Change password", "Change type": "Change type", "Changed Node Count": "Changed Node Count", "Channel": "Channel", "Chassis ID": "<PERSON><PERSON>s <PERSON>", "Check Can Live Migrate Destination": "Check Can Live Migrate Destination", "Check Can Live Migrate Source": "Check Can Live Migrate Source", "Check Complete": "Check Complete", "Check Failed": "Check Failed", "Check In Progress": "Check In Progress", "Checksum": "Checksum", "Chile": "Chile", "China": "China", "Choose a Network Driver": "Choose a Network Driver", "Choose a host to live migrate instance to. If not selected, the scheduler will auto select target host.": "Choose a host to live migrate instance to. If not selected, the scheduler will auto select target host.", "Choose a host to migrate instance to. If not selected, the scheduler will auto select target host.": "Choose a host to migrate instance to. If not selected, the scheduler will auto select target host.", "Choosing a QoS policy can limit bandwidth and DSCP": "Choosing a QoS policy can limit bandwidth and DSCP", "Christmas Island": "Christmas Island", "Cidr": "<PERSON><PERSON><PERSON>", "Cinder Service": "Cinder Service", "Cipher": "Cipher", "Clean Failed": "Clean Failed", "Clean Wait": "Clean Wait", "Cleaning": "Cleaning", "Clear Gateway": "Clear Gateway", "Clear selected": "Clear selected", "Click To View": "Click To View", "Click here for filters.": "Click here for filters.", "Click to Upload": "Click to Upload", "Click to show detail": "Click to show detail", "Clone Volume": "Clone Volume", "Clone volume": "Clone volume", "Close": "Close", "Close External Gateway": "Close External Gateway", "Close all notifications.": "Close all notifications.", "Close external gateway": "Close external gateway", "Cloud": "Cloud", "Cloud Container Engine": "Cloud Container Engine", "Cluster Detail": "Cluster Detail", "Cluster Distro": "Cluster Distro", "Cluster Info": "Cluster Info", "Cluster Management": "Cluster Management", "Cluster Name": "Cluster Name", "Cluster Network": "Cluster Network", "Cluster Template": "Cluster Template", "Cluster Template Detail": "Cluster Template Detail", "Cluster Template Name": "Cluster Template Name", "Cluster Templates": "Cluster Templates", "Cluster Type": "Cluster Type", "Clusters": "Clusters", "Clusters Management": "Clusters Management", "Cocos (Keeling) Islands": "Cocos (Keeling) Islands", "Code": "Code", "Cold Migrate": "Cold Migrate", "Colombia": "Colombia", "Command": "Command", "Command to run to check health": "Command to run to check health", "Command was successfully executed at container {name}.": "Command was successfully executed at container {name}.", "Commas ‘,’ are not allowed to be in a tag name in order to simplify requests that specify lists of tags": "Commas ‘,’ are not allowed to be in a tag name in order to simplify requests that specify lists of tags", "Commit Latency(ms)": "Commit Latency(ms)", "Common Server": "Common Server", "Comoros": "Comoros", "Compute": "Compute", "Compute Hosts": "Compute Hosts", "Compute Live Migration": "Compute Live Migration", "Compute Live Resize Instance": "Compute Live Resize Instance", "Compute Node status": "Compute Node status", "Compute Optimized": "Compute Optimized", "Compute Optimized Info": "Compute Optimized Info", "Compute Optimized Type": "Compute Optimized Type", "Compute Optimized Type with GPU": "Compute Optimized Type with GPU", "Compute Pause Instance": "Compute Pause Instance", "Compute Reboot Instance": "Compute Reboot Instance", "Compute Resume Instance": "Compute Resume Instance", "Compute Service": "Compute Service", "Compute Services": "Compute Services", "Compute Start Instance": "Compute Start Instance", "Compute Stop Instance": "Compute Stop Instance", "Compute Suspend Instance": "Compute Suspend Instance", "Compute Unpause Instance": "Compute Unpause Instance", "Conductor Live Migrate Instance": "Conductor Live Migrate Instance", "Conductor Live Resize Instance": "Conductor Live Resize Instance", "Conductor Migrate Server": "Conductor Migra<PERSON> Server", "Config Overview": "Config Overview", "Configuration": "Configuration", "Configuration Detail": "Configuration Detail", "Configuration Group": "Configuration Group", "Configuration Group ID/Name": "Configuration Group ID/Name", "Configuration Groups": "Configuration Groups", "Configuration Update": "Configuration Update", "Configured Disk (GiB)": "Configured Disk (GiB)", "Configured Memory (GiB)": "Configured Memory (GiB)", "Confirm": "Confirm", "Confirm Config": "Confirm Config", "Confirm Password": "Confirm Password", "Confirm Resize or Migrate": "Confirm Resize or Migrate", "Confirm Shared Key": "Confirm Shared Key", "Confirming Resize or Migrate": "Confirming Resize or Migrate", "Connect Subnet": "Connect Subnet", "Connect router": "Connect router", "Connected Threads": "Connected Threads", "Connection Examples": "Connection Examples", "Connection Information": "Connection Information", "Connection Limit": "Connection Limit", "Consecutive failures needed to report unhealthy": "Consecutive failures needed to report unhealthy", "Console": "<PERSON><PERSON><PERSON>", "Console Interface": "Console Interface", "Console Log": "<PERSON><PERSON><PERSON>", "Consumer": "Consumer", "Container": "Container", "Container Creating": "Container C<PERSON>ting", "Container Deleting": "Container Deleting", "Container Detail": "Container Detail", "Container Format": "Container Format", "Container Killing": "Container Killing", "Container Name": "Container Name", "Container Pausing": "Container Pausing", "Container Rebooting": "Container Rebooting", "Container Rebuilding": "Container Rebuilding", "Container Restarting": "Container Restarting", "Container Starting": "Container Starting", "Container Status": "Container Status", "Container Stopping": "Container Stopping", "Container Unpausing": "Container Unpausing", "Container Version": "Container Version", "Containers": "Containers", "Containers CPU": "Containers CPU", "Containers Disk (GiB)": "Containers Disk (GiB)", "Containers Info": "Containers Info", "Containers Management": "Containers Management", "Containers Memory (MiB)": "Containers Memory (MiB)", "Content": "Content", "Content Type": "Content Type", "Continue": "Continue", "Continue Upload": "Continue Upload", "Control Attribute": "Control Attribute", "Control Attributes": "Control Attributes", "Control Location": "Control Location", "Cook Islands": "Cook Islands", "Copy": "Copy", "Copy File": "Copy File", "CoreOS": "CoreOS", "Costa Rica": "Costa Rica", "Cote D'Ivoire": "Cote D'Ivoire", "Count": "Count", "Crashed": "Crashed", "Create": "Create", "Create Allowed Address Pair": "Create Allowed Address Pair", "Create Application Credentials": "Create Application Credentials", "Create Backup": "Create Backup", "Create Bandwidth Limit Rule": "Create Bandwidth Limit Rule", "Create Bare Metal Node": "Create Bare Metal Node", "Create Capsule": "Create Capsule", "Create Certificate": "Create Certificate", "Create Cluster": "Create Cluster", "Create Cluster Template": "Create Cluster Template", "Create Complete": "Create Complete", "Create Configurations": "Create Configurations", "Create Container": "Create Container", "Create DSCP Marking Rule": "Create DSCP Marking Rule", "Create Database": "Create Database", "Create Database Backup": "Create Database Backup", "Create Database Instance": "Create Database Instance", "Create Default Pool": "Create Default Pool", "Create Domain": "Create Domain", "Create Encryption": "Create Encryption", "Create Extra Spec": "Create Extra Spec", "Create Failed": "Create Failed", "Create Firewall": "Create Firewall", "Create Firewall Policy": "Create Firewall Policy", "Create Flavor": "Create <PERSON>lavor", "Create Folder": "Create Folder", "Create Host Aggregate": "Create Host Aggregate", "Create IPsec Site Connection": "Create IPsec Site Connection", "Create Image": "Create Image", "Create In Progress": "Create In Progress", "Create Instance": "Create Instance", "Create Instance Snapshot": "Create <PERSON><PERSON> Snapshot", "Create Ironic Instance": "Create Ironic Instance", "Create Keypair": "Create Keypair", "Create Listener": "Create Listener", "Create Loadbalancer": "Create Loadbalancer", "Create Network": "Create Network", "Create New Network": "Create New Network", "Create Node": "Create Node", "Create Policy": "Create Policy", "Create Port": "Create Port", "Create Port Forwarding": "Create Port Forwarding", "Create Port Group": "Create Port Group", "Create Project": "Create Project", "Create QoS Policy": "Create QoS Policy", "Create QoS Spec": "Create QoS Spec", "Create RBAC Policy": "Create RBAC Policy", "Create Record Set": "Create Record Set", "Create Role": "Create Role", "Create Router": "Create Router", "Create Rule": "Create Rule", "Create Security Group": "Create Security Group", "Create Segment": "Create Segment", "Create Server Group": "Create Server Group", "Create Share": "Create Share", "Create Share Group": "Create Share Group", "Create Share Group Type": "Create Share Group Type", "Create Share Metadata": "Create Share Metadata", "Create Share Network": "Create Share Network", "Create Share Type": "Create Share Type", "Create Snapshot": "Create Snapshot", "Create Stack": "C<PERSON><PERSON>", "Create Static Route": "Create Static Route", "Create Subnet": "Create Subnet", "Create Time": "Create Time", "Create Transfer": "Create Transfer", "Create Type": "Create Type", "Create User": "Create User", "Create User Group": "Create User Group", "Create VPN": "Create VPN", "Create VPN Endpoint Group": "Create VPN Endpoint Group", "Create VPN IKE Policy": "Create VPN IKE Policy", "Create VPN IPsec Policy": "Create VPN IPsec Policy", "Create Virtual Adapter": "Create Virtual Adapter", "Create Volume": "Create Volume", "Create Volume Backup": "Create Volume Backup", "Create Volume Snapshot": "Create Volume Snapshot", "Create Volume Type": "Create Volume Type", "Create Zone": "Create Zone", "Create a full backup, the system will automatically create a new backup chain, the full backup name is the backup chain name; Create an incremental backup, the system will automatically create an incremental backup under the newly created backup chain.": "Create a full backup, the system will automatically create a new backup chain, the full backup name is the backup chain name; Create an incremental backup, the system will automatically create an incremental backup under the newly created backup chain.", "Create firewall": "Create firewall", "Create host aggregate": "Create host aggregate", "Create image": "Create image", "Create instance": "Create instance", "Create ironic instance": "Create ironic instance", "Create new AZ": "Create new AZ", "Create rule": "Create rule", "Create security group": "Create security group", "Create server group": "Create server group", "Create static route": "Create static route", "Create volume": "Create volume", "Create volume backup": "Create volume backup", "Created": "Created", "Created At": "Created At", "Created Time": "Created Time", "Created Volumes": "Created Volumes", "Creating": "Creating", "Creating From Snapshot": "Creating From Snapshot", "Creation Timeout (Minutes)": "Creation Timeout (Minutes)", "Credential Type": "Credential Type", "Croatia (local name: Hrvatska)": "Croatia (local name: Hrvatska)", "Cuba": "Cuba", "Current Availability Zones": "Current Availability Zones", "Current Compute Host": "Current Compute Host", "Current Connections": "Current Connections", "Current Disk (GiB)": "Current Disk (GiB)", "Current Flavor": "Current Flavor", "Current Host": "Current Host", "Current Interface": "Current Interface", "Current Master Node Count": "Current Master Node <PERSON>", "Current Node Count": "Current Node Count", "Current Password": "Current Password", "Current Path: ": "Current Path: ", "Current Project": "Current Project", "Current Project Images": "Current Project Images", "Current Project Networks": "Current Project Networks", "Current Project QoS Policies": "Current Project QoS Policies", "Current QoS policy name": "Current QoS policy name", "Current Rules": "Current Rules", "Current Status": "Current Status", "Current Storage Backend": "Current Storage Backend", "Current data downloaded.": "Current data downloaded.", "Custom": "Custom", "Custom Headers": "Custom Headers", "Custom ICMP Rule": "Custom ICMP Rule", "Custom Metadata": "Custom Metadata", "Custom Properties Info": "Custom Properties Info", "Custom TCP Rule": "Custom TCP Rule", "Custom Trait": "Custom Trait", "Custom UDP Rule": "Custom UDP Rule", "Cut": "Cut", "Cut File": "Cut File", "Cyprus": "Cyprus", "Czech Republic": "Czech Republic", "DC/OS": "DC/OS", "DCCP": "DCCP", "DEGRADED: One or more of the entity’s components are in ERROR": "DEGRADED: One or more of the entity’s components are in ERROR", "DELETE COMPLETE": "DELETE COMPLETE", "DELETE FAILED": "DELETE FAILED", "DELETE_IN PROGRESS": "DELETE_IN PROGRESS", "DENY": "DENY", "DHCP": "DHCP", "DHCP Agent": "DHCP Agent", "DHCP Agents": "DHCP Agents", "DISK IOPS": "DISK IOPS", "DISK Usage(%)": "DISK Usage(%)", "DNS": "DNS", "DNS Assignment": "DNS Assignment", "DNS Name": "DNS Name", "DNS Nameservers": "DNS Nameservers", "DNS Reverse": "DNS Reverse", "DNS Zones": "DNS Zones", "DNS Zones Detail": "DNS Zones Detail", "DPD Action": "DPD Action", "DPD Interval (sec)": "DPD Interval (sec)", "DPD actions controls the use of Dead Peer Detection Protocol.": "DPD actions controls the use of Dead Peer Detection Protocol.", "DPD timeout (sec)": "DPD timeout (sec)", "DRAINING: The member is not accepting new connections": "DRAINING: The member is not accepting new connections", "DSCP Marking": "DSCP Marking", "Danger": "Danger", "Data Disk": "Data Disk", "Data Disks": "Data Disks", "Data Protection": "Data Protection", "Data Source Type": "Data Source Type", "Database": "Database", "Database Backup Detail": "Database Backup Detail", "Database Disk (GiB)": "Database Disk (GiB)", "Database Flavor": "Database Flavor", "Database Instance": "Database Instance", "Database Instance Detail": "Database Instance Detail", "Database Instance Name": "Database Instance Name", "Database Instance Status": "Database Instance Status", "Database Instances": "Database Instances", "Database Name": "Database Name", "Database Port": "Database Port", "Database Service": "Database Service", "Databases": "Databases", "Datastore": "Datastore", "Datastore Type": "Datastore Type", "Datastore Version": "Datastore Version", "Deactivated": "Deactivated", "Debian": "Debian", "Dedicated": "Dedicated", "Default Policy": "Default Policy", "Default Project": "Default Project", "Default Project ID/Name": "Default Project ID/Name", "Default is slaac, for details, see https://docs.openstack.org/neutron/latest/admin/config-ipv6.html": "Default is slaac, for details, see https://docs.openstack.org/neutron/latest/admin/config-ipv6.html", "Defaults": "De<PERSON>ults", "Defines the admin state of the health monitor.": "Defines the admin state of the health monitor.", "Defines the admin state of the listener.": "Defines the admin state of the listener.", "Defines the admin state of the pool.": "Defines the admin state of the pool.", "Defines the admin state of the port.": "Defines the admin state of the port.", "Degraded": "Degraded", "Delay Interval(s)": "Delay Interval(s)", "Delete": "Delete", "Delete Allowed Address Pair": "Delete Allowed Address Pair", "Delete Application Credential": "Delete Application Credential", "Delete Bandwidth Egress Rules": "Delete Bandwidth Egress Rules", "Delete Bandwidth Ingress Rules": "Delete Bandwidth Ingress Rules", "Delete Capsule": "Delete Capsule", "Delete Certificate": "Delete Certificate", "Delete Cluster": "Delete Cluster", "Delete Cluster Template": "Delete Cluster Template", "Delete Complete": "Delete Complete", "Delete Configuration": "Delete Configuration", "Delete Container": "Delete Container", "Delete DSCP Marking Rules": "Delete DSCP Marking Rules", "Delete Database": "Delete Database", "Delete Database Backup": "Delete Database Backup", "Delete Default Pool": "Delete Default Pool", "Delete Domain": "Delete Domain", "Delete Encryption": "Delete Encryption", "Delete Extra Specs": "Delete Extra Specs", "Delete Failed": "Delete Failed", "Delete File": "Delete File", "Delete Firewall": "Delete Firewall", "Delete Flavor": "Delete Flavor", "Delete Folder": "Delete Folder", "Delete Group": "Delete Group", "Delete Host Aggregate": "Delete Host Aggregate", "Delete IPsec Site Connection": "Delete IPsec Site Connection", "Delete Image": "Delete Image", "Delete In Progress": "Delete In Progress", "Delete Instance": "Delete Instance", "Delete Instance Snapshot": "Delete Instance Snapshot", "Delete Keypair": "Delete Keypair", "Delete Listener": "Delete Listener", "Delete Load Balancer": "Delete Load Balancer", "Delete Member": "Delete Member", "Delete Metadata": "Delete Metadata", "Delete Network": "Delete Network", "Delete Node": "Delete Node", "Delete Policy": "Delete Policy", "Delete Port": "Delete Port", "Delete Port Forwarding": "Delete Port Forwarding", "Delete Port Group": "Delete Port Group", "Delete Project": "Delete Project", "Delete QoS Policy": "Delete QoS Policy", "Delete QoS Spec": "Delete QoS Spec", "Delete RBAC Policy": "Delete RBAC Policy", "Delete Record Set": "Delete Record Set", "Delete Role": "Delete Role", "Delete Router": "Delete Router", "Delete Rule": "Delete Rule", "Delete Security Group": "Delete Security Group", "Delete Server Group": "Delete Server Group", "Delete Share": "Delete Share", "Delete Share Access Rule": "Delete Share Access Rule", "Delete Share Group": "Delete Share Group", "Delete Share Group Type": "Delete Share Group Type", "Delete Share Metadata": "Delete Share Metadata", "Delete Share Network": "Delete Share Network", "Delete Share Server": "Delete Share Server", "Delete Share Type": "Delete Share Type", "Delete Static Route": "Delete Static Route", "Delete Subnet": "Delete Subnet", "Delete User": "Delete User", "Delete VPN": "Delete VPN", "Delete VPN EndPoint Groups": "Delete VPN EndPoint Groups", "Delete VPN IKE Policy": "Delete VPN IKE Policy", "Delete VPN IPsec Policy": "Delete VPN IPsec Policy", "Delete Virtual Adapter": "Delete Virtual Adapter", "Delete Volume": "Delete Volume", "Delete Volume Backup": "Delete Volume Backup", "Delete Volume Snapshot": "Delete Volume Snapshot", "Delete Volume Type": "Delete Volume Type", "Delete Volume on Instance Delete": "Delete Volume on Instance Delete", "Delete Zone": "Delete Zone", "Delete metadata": "Delete metadata", "Deleted": "Deleted", "Deleted At": "Deleted At", "Deleted with the instance": "Deleted with the instance", "Deleting": "Deleting", "Deleting this stack will delete all resources deployed by the stack.": "Deleting this stack will delete all resources deployed by the stack.", "Democratic Republic of the Congo": "Democratic Republic of the Congo", "Denmark": "Denmark", "Denying": "Denying", "Deploy Failed": "Deploy Failed", "Deploy Wait": "Deploy Wait", "Deploying": "Deploying", "Deployment Parameters": "Deployment Parameters", "Description": "Description", "Dest Folder": "Dest Folder", "Destination": "Destination", "Destination CIDR": "Destination CIDR", "Destination IP": "Destination IP", "Destination IP Address/Subnet": "Destination IP Address/Subnet", "Destination Port": "Destination Port", "Destination Port/Port Range": "Destination Port/Port Range", "Detach": "<PERSON><PERSON>", "Detach Instance": "Detach Instance", "Detach Interface": "Detach Interface", "Detach Network": "Detach Network", "Detach Security Group": "Detach Security Group", "Detach Volume": "Detach Volume", "Detach interface": "Detach interface", "Detaching": "Detaching", "Detail": "Detail", "Detail Info": "Detail Info", "Details": "Details", "Details *": "Details *", "Details about the PTR record.": "Details about the PTR record.", "Device": "<PERSON><PERSON>", "Device ID": "Device ID", "Device ID/Name": "Device ID/Name", "Device Owner": "<PERSON><PERSON> Owner", "Devicemapper": "<PERSON><PERSON>mapper", "Direct": "Direct", "Direction": "Direction", "Disable": "Disable", "Disable Cinder Service": "Disable Cinder Service", "Disable Compute Host": "Disable Compute Host", "Disable Compute Service": "Disable Compute Service", "Disable Gateway": "Disable Gateway", "Disable Neutron Agent": "Disable Neutron Agent", "Disable SNAT": "Disable SNAT", "Disable TLS": "Disable TLS", "Disable compute host": "Disable compute host", "Disabled": "Disabled", "Disabling port security will turn off the security group policy protection and anti-spoofing protection on the port. General applicable scenarios: NFV or operation and maintenance Debug.": "Disabling port security will turn off the security group policy protection and anti-spoofing protection on the port. General applicable scenarios: NFV or operation and maintenance Debug.", "Disabling the project will have a negative impact. If the users associated with the project are only assigned to the project, they will not be able to log in": "Disabling the project will have a negative impact. If the users associated with the project are only assigned to the project, they will not be able to log in", "Disassociate": "Disassociate", "Disassociate Floating IP": "Disassociate Floating IP", "Disassociate Floating Ip": "Disassociate Floating Ip", "Disconnect Subnet": "Disconnect Subnet", "Discovery URL": "Discovery URL", "Disk": "Disk", "Disk (GiB)": "Disk (GiB)", "Disk Format": "Disk Format", "Disk Info": "Disk Info", "Disk Tag": "Disk Tag", "Disk allocation (GiB)": "Disk allocation (GiB)", "Disk size is limited by the min disk of flavor, image, etc.": "Disk size is limited by the min disk of flavor, image, etc.", "Djibouti": "Djibouti", "Do Build And Run Instance": "Do Build And Run Instance", "Do HH:mm": "Do HH:mm", "Do not reset the normally mounted volume to the \"available\"、\"maintenance\" or \"error\" status. The reset state does not remove the volume from the instance. If you need to remove the volume from the instance, please go to the console of the corresponding project and use the \"detach\" operation.": "Do not reset the normally mounted volume to the \"available\"、\"maintenance\" or \"error\" status. The reset state does not remove the volume from the instance. If you need to remove the volume from the instance, please go to the console of the corresponding project and use the \"detach\" operation.", "Do not set with a backend": "Do not set with a backend", "Docker": "<PERSON>er", "Docker Hub": "<PERSON><PERSON>", "Docker Storage Driver": "Docker Storage Driver", "Docker Swarm": "Docker Swarm", "Docker Swarm Mode": "Docker Swarm Mode", "Docker Volume Size (GiB)": "Docker Volume Size (GiB)", "Domain": "Domain", "Domain Detail": "Domain Detail", "Domain ID": "Domain ID", "Domain ID/Name": "Domain ID/Name", "Domain Manager": "Domain Manager", "Domain Name": "Domain Name", "Domain name ending in.": "Domain name ending in.", "Domains": "Domains", "Dominica": "Dominica", "Down": "Down", "Download": "Download", "Download File": "Download File", "Download Image": "Download Image", "Download all data": "Download all data", "Download canceled!": "Download canceled!", "Download current data": "Download current data", "Download progress": "Download progress", "Downloading": "Downloading", "Draining": "Draining", "Driver": "Driver", "Driver Handles Share Servers": "Driver Handles Share Servers", "Driver Info": "Driver Info", "Driver Interface": "Driver Interface", "Duplicate tag name: {tag}": "Duplicate tag name: {tag}", "EGP": "EGP", "ENTRYPOINT": "ENTRYPOINT", "ESP": "ESP", "Each instance belongs to at least one security group, which needs to be specified when it is created. Instances in the same security group can communicate with each other on the network, and instances in different security groups are disconnected from the internal network by default.": "Each instance belongs to at least one security group, which needs to be specified when it is created. Instances in the same security group can communicate with each other on the network, and instances in different security groups are disconnected from the internal network by default.", "Each new connection request is assigned to the next server in order, and all requests are finally divided equally among all servers. Commonly used for short connection services, such as HTTP services.": "Each new connection request is assigned to the next server in order, and all requests are finally divided equally among all servers. Commonly used for short connection services, such as HTTP services.", "Each server can have up to 50 tags": "Each server can have up to 50 tags", "East Timor": "East Timor", "Ecuador": "Ecuador", "Edit": "Edit", "Edit Bandwidth Egress Limit Rule": "Edit <PERSON>width Egress Limit Rule", "Edit Bandwidth Ingress Limit Rule": "Edit Bandwidth Ingress Limit Rule", "Edit Bare Metal Node": "Edit Bare Metal Node", "Edit Consumer": "Edit Consumer", "Edit Container": "Edit Container", "Edit DSCP Marking Rule": "Edit DSCP Marking Rule", "Edit Default Pool": "<PERSON><PERSON>", "Edit Domain": "Edit Domain", "Edit Domain Permission": "Edit Domain Permission", "Edit Extra Spec": "Edit Extra Spec", "Edit Flavor": "<PERSON>", "Edit Health Monitor": "Edit Health Monitor", "Edit Host Aggregate": "Edit Host Aggregate", "Edit IPsec Site Connection": "Edit IPsec Site Connection", "Edit Image": "Edit Image", "Edit Instance": "Edit Instance", "Edit Instance Snapshot": "Edit Instance Snapshot", "Edit Listener": "Edit Listener", "Edit Load Balancer": "<PERSON> <PERSON><PERSON>", "Edit Member": "Edit Member", "Edit Metadata": "<PERSON>", "Edit Port": "Edit Port", "Edit Port Forwarding": "Edit Port Forwarding", "Edit Port Group": "Edit Port Group", "Edit Project": "Edit Project", "Edit QoS Policy": "Edit QoS Policy", "Edit Quota": "<PERSON>", "Edit Role": "Edit Role", "Edit Router": "Edit Router", "Edit Rule": "Edit Rule", "Edit Share Metadata": "Edit Share Metadata", "Edit Subnet": "Edit Subnet", "Edit System Permission": "Edit System Permission", "Edit User": "Edit User", "Edit User Group": "Edit User Group", "Edit VPN": "Edit VPN", "Edit VPN EndPoint Groups": "Edit VPN EndPoint Groups", "Edit VPN IKE Policy": "Edit VPN IKE Policy", "Edit VPN IPsec Policy": "Edit VPN IPsec Policy", "Edit Volume Backup": "Edit Volume Backup", "Edit host aggregate": "Edit host aggregate", "Edit metadata": "Edit metadata", "Edit quota": "Edit quota", "Edit rule": "Edit rule", "Editing only changes the content of the file, not the file name.": "Editing only changes the content of the file, not the file name.", "Effective Mode": "Effective Mode", "Effective mode after configuration changes": "Effective mode after configuration changes", "Egress": "Egress", "Egress Policy": "Egress Policy", "Egress Policy ID": "Egress Policy ID", "Egress Policy Name": "Egress Policy Name", "Egypt": "Egypt", "Eject": "Eject", "El Salvador": "El Salvador", "Email": "Email", "Email Address": "Email Address", "Email for the zone. Used in SOA records for the zone.": "Email for the zone. Used in SOA records for the zone.", "Enable": "Enable", "Enable Admin State": "Enable Admin State", "Enable Compute Host": "Enable Compute Host", "Enable Compute Service": "Enable Compute Service", "Enable DHCP": "Enable DHCP", "Enable Domain": "Enable Domain", "Enable Floating IP": "Enable Floating IP", "Enable Health Check": "Enable Health Check", "Enable Health Monitor": "Enable Health Monitor", "Enable Load Balancer": "Enable Load Balancer", "Enable Neutron Agent": "Enable Neutron Agent", "Enable Project": "Enable Project", "Enable QoS Policy": "Enable QoS Policy", "Enable Registry": "Enable Registry", "Enable SNAT": "Enable SNAT", "Enable Service": "Enable Service", "Enable User": "Enable User", "Enable auto heal": "Enable auto heal", "Enable auto remove": "Enable auto remove", "Enable compute host": "Enable compute host", "Enable interactive mode": "Enable interactive mode", "Enabled": "Enabled", "Enabled Load Balancer for Master Nodes": "Enabled Load Balancer for Master Nodes", "Enabled Network": "Enabled Network", "Encapsulation Mode": "Encapsulation Mode", "Encrypted": "Encrypted", "Encryption": "Encryption", "Encryption Algorithm": "Encryption Algorithm", "Encryption Info": "Encryption Info", "End Time": "End Time", "Endpoint Counts": "Endpoint Counts", "Endpoints": "Endpoints", "Engine ID": "Engine ID", "Enroll": "Enroll", "Enter Maintenance Mode": "Enter Maintenance Mode", "Enter an integer value between 1 and 65535.": "Enter an integer value between 1 and 65535.", "Enter query conditions to filter": "Enter query conditions to filter", "Entered: {length, plural, =1 {one character} other {# characters} }(maximum {maxCount} characters)": "Entered: {length, plural, =1 {one character} other {# characters} }(maximum {maxCount} characters)", "Environment": "Environment", "Environment Variable": "Environment Variable", "Environment Variables": "Environment Variables", "Ephemeral Disk (GiB)": "Ephemeral Disk (GiB)", "Equatorial Guinea": "Equatorial Guinea", "Eritrea": "Eritrea", "Error": "Error", "Error Deleting": "<PERSON>rro<PERSON>", "Error Extending": "Error Extending", "Error Restoring": "Error <PERSON>", "Estonia": "Estonia", "Ether Type": "Ether Type", "Ethiopia": "Ethiopia", "Event": "Event", "Event Time": "Event Time", "Evictions": "Evictions", "Execute Command": "Execute Command", "Execution Result": "Execution Result", "Existing Volume": "Existing Volume", "Exit Policy": "Exit Policy", "Exp: ": "Exp: ", "Expand": "Expand", "Expand Advanced Options": "Expand Advanced Options", "Expired Time": "Expired Time", "Expires At": "Expires At", "Export Location": "Export Location", "Export Locations": "Export Locations", "Exposed Ports": "Exposed Ports", "Extend Root Volume": "Extend Root Volume", "Extend Share": "Extend Share", "Extend Volume": "Extend Volume", "Extend volume": "Extend volume", "Extending": "Extending", "Extending Error": "Extending Error", "External": "External", "External Fixed IP": "External Fixed IP", "External Fixed IPs": "External Fixed IPs", "External Gateway": "External Gateway", "External IP": "External IP", "External IP(V4)": "External IP(V4)", "External IP(V6)": "External IP(V6)", "External Network": "External Network", "External Network ID/Name": "External Network ID/Name", "External Network Info": "External Network Info", "External Networks": "External Networks", "External Port": "External Port", "External Port/Port Range": "External Port/Port Range", "Extra Infos": "Extra Infos", "Extra Specs": "Extra Specs", "FAKE": "FAKE", "FLAT": "FLAT", "Fail Rollback": "<PERSON><PERSON>", "Failed": "Failed", "Failover Segment": "Failover Segment", "Falkland Islands (Malvinas)": "Falkland Islands (Malvinas)", "Faroe Islands": "Faroe Islands", "Fault": "<PERSON><PERSON>", "Fedora": "<PERSON><PERSON>", "Fiji": "Fiji", "File": "File", "File System Used Space": "File System Used Space", "File URL": "File URL", "Filename": "Filename", "Files: {names}": "Files: {names}", "Fill In The Parameters": "Fill In The Parameters", "Fingerprint": "Fingerprint", "Finish Resize": "Finish Resize", "Finland": "Finland", "Firewall": "Firewall", "Firewall Detail": "Firewall Detail", "Firewall Policies": "Firewall Policies", "Firewall Policy": "Firewall Policy", "Firewall Port": "Firewall Port", "Firewall Rule": "Firewall Rule", "Firewall Rules": "Firewall Rules", "Firewalls": "Firewalls", "Fixed IP": "Fixed IP", "Fixed IP Address": "Fixed IP Address", "Fixed IPs": "Fixed IPs", "Fixed Network": "Fixed Network", "Fixed Subnet": "Fixed Subnet", "Flavor": "Flavor", "Flavor Detail": "<PERSON><PERSON><PERSON>", "Flavor Info": "Flavor Info", "Flavor Name": "Flavor Name", "Flavor families, used to configure the instance flavor classification": "Flavor families, used to configure the instance flavor classification", "Flavor of Master Nodes": "<PERSON><PERSON><PERSON> of Master Nodes", "Flavor of Nodes": "Flavor of Nodes", "Flavors": "Flavors", "Floating IP": "Floating IP", "Floating IP Address": "Floating IP Address", "Floating IP Enabled": "Floating IP Enabled", "Floating IPs": "Floating IPs", "Floating Ip": "Floating Ip", "Floating Ip Address": "Floating Ip Address", "Floating Ip Detail": "Floating Ip Detail", "Floating ip has already been associate, Please check Force release": "Floating ip has already been associate, Please check Force release", "Folder Detail": "Folder Detail", "Folder Name": "Folder Name", "For GPU type, you need to install GPU drivers in the instance operating system.": "For GPU type, you need to install GPU drivers in the instance operating system.", "For GRE networks, valid segmentation IDs are 1 to 4294967295": "For GRE networks, valid segmentation IDs are 1 to 4294967295", "For VLAN networks, valid segmentation IDs are 1 to 4094": "For VLAN networks, valid segmentation IDs are 1 to 4094", "For VXLAN networks, valid segmentation IDs are 1 to 16777215": "For VXLAN networks, valid segmentation IDs are 1 to 16777215", "Forbidden": "Forbidden", "Forbidden Domain": "Forbidden Domain", "Forbidden Project": "Forbidden Project", "Forbidden User": "Forbidden User", "Forbidden the domain will have a negative impact, all project and user in domain will be forbidden": "Forbidden the domain will have a negative impact, all project and user in domain will be forbidden", "Force Delete": "Force Delete", "Force Delete Container": "Force Delete Container", "Force Delete Share Instance": "Force Delete Share Instance", "Force release": "Force release", "Force shutdown must be checked!": "Force shutdown must be checked!", "Forced Down": "Forced Down", "Forced Shutdown": "Forced Shutdown", "Forced shutdown may result in data loss or file system damage. You can also take the initiative to shut down and perform operations.": "Forced shutdown may result in data loss or file system damage. You can also take the initiative to shut down and perform operations.", "Forgot your password?": "Forgot your password?", "Format": "Format", "Forward Slash ‘/’ is not allowed to be in a tag name": "Forward Slash ‘/’ is not allowed to be in a tag name", "France": "France", "Free": "Free", "FreeBSD": "FreeBSD", "French Guiana": "French Guiana", "French Polynesia": "French Polynesia", "Frequent login failure will cause the account to be temporarily locked, please operate after 5 minutes": "Frequent login failure will cause the account to be temporarily locked, please operate after 5 minutes", "From port": "From port", "Front End": "Front End", "Frontend": "Frontend", "Full": "Full", "Full Backup": "Full Backup", "GPU Count": "GPU Count", "GPU Info": "GPU Info", "GPU Model": "GPU Model", "GPU Parameters": "GPU Parameters", "GPU Type": "GPU Type", "GPU model, used when configuring Compute Optimized Type with GPU": "GPU model, used when configuring Compute Optimized Type with GPU", "GPU pass-through will load GPU devices directly to the instance for use. VGPU is a GPU virtualization solution. GPU resources will be segmented and distributed to multiple instances for shared use.": "GPU pass-through will load GPU devices directly to the instance for use. VGPU is a GPU virtualization solution. GPU resources will be segmented and distributed to multiple instances for shared use.", "GRE": "GRE", "Gabon": "Gabon", "Gambia": "Gambia", "Gateway": "Gateway", "Gateway IP": "Gateway IP", "Gateway Time-out (code: 504) ": "Gateway Time-out (code: 504) ", "Gateway ip {gateway_ip} conflicts with allocation pool {pool}": "Gateway ip {gateway_ip} conflicts with allocation pool {pool}", "General Purpose": "General Purpose", "Generated Time": "Generated Time", "Georgia": "Georgia", "Germany": "Germany", "Get OpenRC file": "Get OpenRC file", "Get Token": "Get Token", "Get {name} detail error.": "Get {name} detail error.", "Get {name} error.": "Get {name} error.", "Ghana": "Ghana", "Gibraltar": "Gibraltar", "Given IP": "Given IP", "Glance": "Glance", "Glance Image": "Glance Image", "Global Setting": "Global Setting", "GlusterFS": "GlusterFS", "Grant Databases Access": "Grant Databases Access", "Greece": "Greece", "Greenland": "Greenland", "Grenada": "Grenada", "Guadeloupe": "Guadeloupe", "Guam": "Guam", "Guatemala": "Guatemala", "Guinea": "Guinea", "Guinea Bissau": "Guinea Bissau", "Guyana": "Guyana", "HDFS": "HDFS", "HEALTHY": "HEALTHY", "HTTP Proxy": "HTTP Proxy", "HTTP Version not supported (code: 505) ": "HTTP Version not supported (code: 505) ", "HTTPS Proxy": "HTTPS Proxy", "Haiti": "Haiti", "Hard Reboot": "Hard Reboot", "Hard Rebooting": "Hard Rebooting", "Hash": "Hash", "Health Check CMD": "Health Check CMD", "Health Check Interval": "Health Check Interval", "Health Check Retries": "Health Check Retries", "Health Check Timeout": "Health Check Timeout", "Health Checking Log": "Health Checking Log", "Health Inspection": "Health Inspection", "New Health Inspection": "New Health Inspection", "Health Monitor": "Health Monitor", "Health Monitor Delay": "Health Monitor Delay", "Health Monitor Detail": "Health Monitor Detail", "Health Monitor Max Retries": "Health Monitor Max Retries", "Health Monitor Name": "Health Monitor Name", "Health Monitor Timeout": "Health Monitor Timeout", "Health Monitor Type": "Health Monitor Type", "Health Status": "Health Status", "HealthMonitor Type": "HealthMonitor Type", "Healthy": "Healthy", "Heartbeat Timestamp": "Heartbeat Timestamp", "Hello, {name}": "Hello, {name}", "Heterogeneous Computing": "Heterogeneous Computing", "Hidden": "Hidden", "Hide Advanced Options": "Hide Advanced Options", "Hide Default Firewalls": "Hide Default Firewalls", "Hide Default Policies": "<PERSON>de Default Policies", "Hide Default Rules": "Hide Default Rules", "High Clock Speed": "High Clock Speed", "Home": "Home", "Home page": "Home page", "Honduras": "Honduras", "Hong Kong": "Hong Kong", "Host": "Host", "Host Aggregate": "Host Aggregate", "Host Aggregates": "Host Aggregates", "Host Average Network IO": "Host Average Network IO", "Host CPU Usage": "Host CPU Usage", "Host Detail": "Host Detail", "Host Disk Average IOPS": "Host Disk Average IOPS", "Host Memory Usage": "Host Memory Usage", "Host Name": "Host Name", "Host Routes": "Host Routes", "Host Routes Format Error(e.g. *************/24,***********)": "Host Routes Format Error(e.g. *************/24,***********)", "Host Routes Format Error(e.g. ::0a38:01fe/24,::0a38:01fe)": "Host Routes Format Error(e.g. ::0a38:01fe/24,::0a38:01fe)", "Hostname": "Hostname", "Hosts": "Hosts", "Hosts Detail": "Hosts Detail", "Hungary": "Hungary", "Hypervisor Detail": "Hypervisor Detail", "Hypervisors": "Hypervisors", "ICMP": "ICMP", "ICMP Code": "ICMP Code", "ICMP Type": "ICMP Type", "ICMP Type/ICMP Code": "ICMP Type/ICMP Code", "ID": "ID", "ID/Floating IP": "ID/Floating IP", "ID/Name": "ID/Name", "IGMP": "IGMP", "IKE Policies": "IKE Policies", "IKE Policy": "IKE Policy", "IKE Version": "IKE Version", "IP": "IP", "IP Address": "IP Address", "IP Distribution Mode": "IP Distribution Mode", "IP Protocol": "IP Protocol", "IP Usage": "IP Usage", "IP Version": "IP Version", "IP address allocation polls, one enter per line(e.g. ***********,***********00)": "IP address allocation polls, one enter per line(e.g. ***********,***********00)", "IP address allocation polls, one enter per line(e.g. {ip})": "IP address allocation polls, one enter per line(e.g. {ip})", "IPMI Address": "IPMI Address", "IPMI Bridge": "IPMI Bridge", "IPMI Password": "IPMI Password", "IPMI Port": "IPMI Port", "IPMI Privilege Level": "IPMI Privilege Level", "IPMI Protocol Version": "IPMI Protocol Version", "IPMI Username": "IPMI Username", "IPMITool": "IPMITool", "IPXE": "IPXE", "IPsec Policies": "IPsec Policies", "IPsec Policy": "IPsec Policy", "IPsec Site Connection": "IPsec Site Connection", "IPsec Site Connections": "IPsec Site Connections", "IPsec site connection Detail": "IPsec site connection Detail", "IPv4": "IPv4", "IPv4 Address": "IPv4 Address", "IPv6": "IPv6", "IPv6 Address": "IPv6 Address", "IPv6 Address Record": "IPv6 Address Record", "IPv6-Encap": "IPv6-Encap", "IPv6-Frag": "IPv6-Frag", "IPv6-ICMP": "IPv6-ICMP", "IPv6-NoNxt": "IPv6-NoNxt", "IPv6-Opts": "IPv6-Opts", "IPv6-Route": "IPv6-Route", "ISO - Optical disc image format": "ISO - Optical disc image format", "Iceland": "Iceland", "Id": "Id", "Identifier of the physical port on the switch to which node’s port is connected to": "Identifier of the physical port on the switch to which node’s port is connected to", "Identity": "Identity", "If \"Enable\" fails to roll back, the resource will be deleted after the creation fails; if \"Disable\" fails to roll back, the resource will be retained after the creation fails.": "If \"Enable\" fails to roll back, the resource will be deleted after the creation fails; if \"Disable\" fails to roll back, the resource will be retained after the creation fails.", "If OS is Linux, system will reset root password, if OS is Windows, system will reset Administrator password.": "If OS is Linux, system will reset root password, if OS is Windows, system will reset Administrator password.", "If an instance is using this flavor, deleting it will cause the instance's flavor data to be missing. Are you sure to delete {name}?": "If an instance is using this flavor, deleting it will cause the instance's flavor data to be missing. Are you sure to delete {name}?", "If checked, the network will be enable.": "If checked, the network will be enable.", "If exposed port is specified, this parameter will be ignored.": "If exposed port is specified, this parameter will be ignored.", "If it is an SNI type certificate, a domain name needs to be specified": "If it is an SNI type certificate, a domain name needs to be specified", "If it’s not set, the value of this in the template will be used.": "If it’s not set, the value of this in the template will be used.", "If no gateway is specified, the first IP address will be defaulted.": "If no gateway is specified, the first IP address will be defaulted.", "If not provided, the roles assigned to the application credential will be the same as the roles in the current token.": "If not provided, the roles assigned to the application credential will be the same as the roles in the current token.", "If nova-compute on the host is disabled, it will be forbidden to be selected as the target host.": "If nova-compute on the host is disabled, it will be forbidden to be selected as the target host.", "If set then all tenants will be able to see this share.": "If set then all tenants will be able to see this share.", "If the capacity of the disk is large, the type modify operation may take several hours. Please be cautious.": "If the capacity of the disk is large, the type modify operation may take several hours. Please be cautious.", "If the listener has an SNI certificate installed, it cannot be removed. Please delete the listener or replace the SNI certificate": "If the listener has an SNI certificate installed, it cannot be removed. Please delete the listener or replace the SNI certificate", "If the root disk has a snapshot, it will affect the deletion of the original disk during reconstruction or the recovery of the instance snapshot.": "If the root disk has a snapshot, it will affect the deletion of the original disk during reconstruction or the recovery of the instance snapshot.", "If the value is set to 0, it means unlimited": "If the value is set to 0, it means unlimited", "If the volume associated with the snapshot has changed the volume type, please modify this option manually; if the volume associated with the snapshot keeps the volume type unchanged, please ignore this option. (no need to change).": "If the volume associated with the snapshot has changed the volume type, please modify this option manually; if the volume associated with the snapshot keeps the volume type unchanged, please ignore this option. (no need to change).", "If this parameter is selected, resumable uploads are supported, but the total upload time may be increased by a small amount. Images smaller than 200M are not recommended.": "If this parameter is selected, resumable uploads are supported, but the total upload time may be increased by a small amount. Images smaller than 200M are not recommended.", "If this parameter is specified, Zun will create a security group with a set of rules to open the ports that should be exposed, and associate the security group to the container.": "If this parameter is specified, <PERSON><PERSON> will create a security group with a set of rules to open the ports that should be exposed, and associate the security group to the container.", "If you are not authorized to access any project, or if the project you are involved in has been deleted or disabled, contact the platform administrator to reassign the project": "If you are not authorized to access any project, or if the project you are involved in has been deleted or disabled, contact the platform administrator to reassign the project", "If you are not sure which authentication method to use, please contact your administrator.": "If you are not sure which authentication method to use, please contact your administrator.", "If you choose a port which subnet is different from the subnet of LB, please ensure connectivity between the two.": "If you choose a port which subnet is different from the subnet of LB, please ensure connectivity between the two.", "If you do not fill in parameters such as cpus, memory_mb, local_gb, cpu_arch, etc., you can automatically inject the configuration and Mac address of the physical machine by performing the \"Auto Inspect\" operation.": "If you do not fill in parameters such as cpus, memory_mb, local_gb, cpu_arch, etc., you can automatically inject the configuration and Mac address of the physical machine by performing the \"Auto Inspect\" operation.", "If you still want to keep the disk data, it is recommended that you create a backup for the disk before deleting.": "If you still want to keep the disk data, it is recommended that you create a backup for the disk before deleting.", "Illegal JSON scheme": "Illegal JSON scheme", "Image": "Image", "Image & OS": "Image & OS", "Image Backup": "Image Backup", "Image Detail": "Image Detail", "Image Driver": "Image Driver", "Image Info": "Image Info", "Image Name": "Image Name", "Image Pending Upload": "Image Pending Upload", "Image Pulling": "Image Pulling", "Image Size": "Image Size", "Image Snapshot Pending": "Image Snapshot Pending", "Image Uploading": "Image Uploading", "Images": "Images", "Immediate effect": "Immediate effect", "Immediately delete": "Immediately delete", "Implied Roles": "Implied Roles", "Import Keypair": "Import Keypair", "Import Metadata": "Import Metadata", "Import metadata": "Import metadata", "Importing": "Importing", "In Cluster": "In Cluster", "In Use": "In Use", "In general, administrator for Windows, root for Linux, please fill by image uploading.": "In general, administrator for Windows, root for Linux, please fill by image uploading.", "In order to avoid data loss, the instance will shut down and interrupt your business. Please confirm carefully.": "In order to avoid data loss, the instance will shut down and interrupt your business. Please confirm carefully.", "In the last 30 days": "In the last 30 days", "In the last 7 days": "In the last 7 days", "In the last hour": "In the last hour", "In-use": "In-use", "Inactive": "Inactive", "Increment Backup": "Increment Backup", "Incremental": "Incremental", "Incremental Backup": "Incremental Backup", "India": "India", "Indicates whether this VPN can only respond to connections or both respond to and initiate connections.": "Indicates whether this VPN can only respond to connections or both respond to and initiate connections.", "Indonesia": "Indonesia", "Infinity": "Infinity", "Info": "Info", "Ingress": "Ingress", "Ingress Policy": "Ingress Policy", "Ingress Policy ID": "Ingress Policy ID", "Ingress Policy Name": "Ingress Policy Name", "Init Complete": "Init Complete", "Init Failed": "Init Failed", "Init In Progress": "Init In Progress", "Initial Admin User": "Initial Admin User", "Initial Databases": "Initial Databases", "Initial Volume Size": "Initial Volume Size", "Initialize Databases": "Initialize Databases", "Initiator Mode": "Initiator Mode", "Input destination port or port range (example: 80 or 80:160)": "Input destination port or port range (example: 80 or 80:160)", "Input external port or port range (example: 80 or 80:160)": "Input external port or port range (example: 80 or 80:160)", "Input internal port or port range (example: 80 or 80:160)": "Input internal port or port range (example: 80 or 80:160)", "Input source port or port range (example: 80 or 80:160)": "Input source port or port range (example: 80 or 80:160)", "Insecure Registry": "Insecure Registry", "Insert": "Insert", "Insert After": "Insert After", "Insert Before": "Insert Before", "Insert Rule": "Insert Rule", "Inspect Failed": "Inspect Failed", "Inspecting": "Inspecting", "Instance": "Instance", "Instance \"{ name }\" has already been locked.": "Instance \"{ name }\" has already been locked.", "Instance \"{ name }\" is ironic, can not soft reboot it.": "Instance \"{ name }\" is ironic, can not soft reboot it.", "Instance \"{ name }\" is locked, can not delete it.": "Instance \"{ name }\" is locked, can not delete it.", "Instance \"{ name }\" is locked, can not pause it.": "Instance \"{ name }\" is locked, can not pause it.", "Instance \"{ name }\" is locked, can not reboot it.": "Instance \"{ name }\" is locked, can not reboot it.", "Instance \"{ name }\" is locked, can not resume it.": "Instance \"{ name }\" is locked, can not resume it.", "Instance \"{ name }\" is locked, can not soft reboot it.": "Instance \"{ name }\" is locked, can not soft reboot it.", "Instance \"{ name }\" is locked, can not start it.": "Instance \"{ name }\" is locked, can not start it.", "Instance \"{ name }\" is locked, can not stop it.": "Instance \"{ name }\" is locked, can not stop it.", "Instance \"{ name }\" is locked, can not suspend it.": "Instance \"{ name }\" is locked, can not suspend it.", "Instance \"{ name }\" is locked, can not unpause it.": "Instance \"{ name }\" is locked, can not unpause it.", "Instance \"{ name }\" is not locked, can not unlock it.": "Instance \"{ name }\" is not locked, can not unlock it.", "Instance \"{ name }\" status is not active, can not soft reboot it.": "Instance \"{ name }\" status is not active, can not soft reboot it.", "Instance \"{ name }\" status is not in active or shutoff, can not reboot it.": "Instance \"{ name }\" status is not in active or shutoff, can not reboot it.", "Instance \"{ name }\" status is not in active or suspended, can not stop it.": "Instance \"{ name }\" status is not in active or suspended, can not stop it.", "Instance \"{ name }\" status is not in active, can not pause it.": "Instance \"{ name }\" status is not in active, can not pause it.", "Instance \"{ name }\" status is not in active, can not suspend it.": "Instance \"{ name }\" status is not in active, can not suspend it.", "Instance \"{ name }\" status is not in paused, can not unpause it.": "Instance \"{ name }\" status is not in paused, can not unpause it.", "Instance \"{ name }\" status is not in suspended, can not resume it.": "Instance \"{ name }\" status is not in suspended, can not resume it.", "Instance \"{ name }\" status is not shutoff, can not start it.": "Instance \"{ name }\" status is not shutoff, can not start it.", "Instance Addr": "Instance Addr", "Instance Architecture": "Instance Architecture", "Instance Console Log": "Instance Console Log", "Instance Detail": "Instance Detail", "Instance ID": "Instance ID", "Instance IP": "Instance IP", "Instance Info": "Instance Info", "Instance Port": "Instance Port", "Instance Related": "Instance Related", "Instance Snapshot": "Instance Snapshot", "Instance Snapshot Detail": "Instance Snapshot Detail", "Instance Snapshot Name": "Instance Snapshot Name", "Instance Snapshots": "Instance Snapshots", "Instance Status": "Instance Status", "Instance UUID": "Instance UUID", "Instance-HA": "Instance-HA", "Instances": "Instances", "Instances \"{ name }\" are locked, can not delete them.": "Instances \"{ name }\" are locked, can not delete them.", "Insufficient {name} quota to create resources (left { quota }, input { input }).": "Insufficient {name} quota to create resources (left { quota }, input { input }).", "Interface Info": "Interface Info", "Interface Name:": "Interface Name:", "Interface for vendor-specific functionality on this node": "Interface for vendor-specific functionality on this node", "Interface used for attaching and detaching volumes on this node": "Interface used for attaching and detaching volumes on this node", "Interface used for configuring RAID on this node": "Interface used for configuring RAID on this node", "Interfaces": "Interfaces", "Internal Ip Address": "Internal Ip Address", "Internal Network Bandwidth (Gbps)": "Internal Network Bandwidth (Gbps)", "Internal Port": "Internal Port", "Internal Port/Port Range": "Internal Port/Port Range", "Internal Server Error (code: 500) ": "Internal Server Error (code: 500) ", "Invalid": "Invalid", "Invalid CIDR.": "Invalid CIDR.", "Invalid IP Address": "Invalid IP Address", "Invalid IP Address and Port": "Invalid IP Address and Port", "Invalid Mac Address. Please Use \":\" as separator.": "Invalid Mac Address. Please Use \":\" as separator.", "Invalid Tag Value: {tag}": "Invalid Tag Value: {tag}", "Invalid combination": "Invalid combination", "Invalid: ": "Invalid: ", "Invalid: Allocation Pools Format Error(e.g. ***********,***********00) and start ip should be less than end ip": "Invalid: Allocation Pools Format Error(e.g. ***********,***********00) and start ip should be less than end ip", "Invalid: Allocation Pools Format Error(e.g. fd00:dead:beef:58::9,fd00:dead:beef:58::13) and start ip should be less than end ip": "Invalid: Allocation Pools Format Error(e.g. fd00:dead:beef:58::9,fd00:dead:beef:58::13) and start ip should be less than end ip", "Invalid: CIDR Format Error(e.g. **********/24)": "Invalid: CIDR Format Error(e.g. **********/24)", "Invalid: DNS Format Error(e.g. 1001:1001::)": "Invalid: DNS Format Error(e.g. 1001:1001::)", "Invalid: DNS Format Error(e.g. ***************)": "Invalid: DNS Format Error(e.g. ***************)", "Invalid: Domain name cannot be duplicated": "Invalid: Domain name cannot be duplicated", "Invalid: Password must be the same with confirm password.": "Invalid: Password must be the same with confirm password.", "Invalid: Please input a valid ip": "Invalid: Please input a valid ip", "Invalid: Please input a valid ipv4": "Invalid: Please input a valid ipv4", "Invalid: Please input a valid ipv6.": "Invalid: Please input a valid ipv6.", "Invalid: Project name can not be chinese": "Invalid: Project name can not be chinese", "Invalid: Project names in the domain can not be repeated": "Invalid: Project names in the domain can not be repeated", "Invalid: Quota value(s) cannot be less than the current usage value(s): { used } used.": "Invalid: Quota value(s) cannot be less than the current usage value(s): { used } used.", "Invalid: User Group names in the domain can not be repeated": "Invalid: User Group names in the domain can not be repeated", "Invalid: User names in the domain can not be repeated": "Invalid: User names in the domain can not be repeated", "Ip Address": "Ip Address", "Iran (Islamic Republic of)": "Iran (Islamic Republic of)", "Iraq": "Iraq", "Ireland": "Ireland", "Ironic Instance": "Ironic Instance", "Ironic Instance Name": "Ironic Instance Name", "Is Current Project": "Is Current Project", "Is Public": "Is Public", "Is admin only": "Is admin only", "Is associate to floating ip: ": "Is associate to floating ip: ", "Is external network port": "Is external network port", "Isolate": "Isolate", "Isolate(No multithreading)": "Isolate(No multithreading)", "Israel": "Israel", "It is IPv6 type.": "It is IPv6 type.", "It is recommended that the { instanceType } instance simultaneously set large page memory to large. { instanceType } instances also require faster memory addressing capabilities.": "It is recommended that the { instanceType } instance simultaneously set large page memory to large. { instanceType } instances also require faster memory addressing capabilities.", "It is recommended that you perform this cloning operation on a disk without any reading/writing": "It is recommended that you perform this cloning operation on a disk without any reading/writing", "It is recommended that you use the private network address 10.0.0.0/8, **********/12, ***********/16": "It is recommended that you use the private network address 10.0.0.0/8, **********/12, ***********/16", "It is recommended that { instanceType } instance simultaneously set NUMA affinity policy for PCIE device to force or priority matching. This configuration can further improve PCIE computing performance.": "It is recommended that { instanceType } instance simultaneously set NUMA affinity policy for PCIE device to force or priority matching. This configuration can further improve PCIE computing performance.", "It is recommended to install and use this agent. The instance created with this image can be used to modify the password (qemu_guest_agent needs to be installed when creating the image).": "It is recommended to install and use this agent. The instance created with this image can be used to modify the password (qemu_guest_agent needs to be installed when creating the image).", "It is recommended to refer to the following description format, otherwise it may not be effective": "It is recommended to refer to the following description format, otherwise it may not be effective", "It is recommended to set CPU binding strategy as binding on { instanceType } instance. This configuration further improves the performance of the instance CPU.": "It is recommended to set CPU binding strategy as binding on { instanceType } instance. This configuration further improves the performance of the instance CPU.", "It is recommended to set the CPU thread binding policy as thread binding in { instanceType } instance, which can further improve the CPU performance of instance.": "It is recommended to set the CPU thread binding policy as thread binding in { instanceType } instance, which can further improve the CPU performance of instance.", "It is suggested to use the marked AZ directly, too much AZ will lead to the fragmentation of available resources": "It is suggested to use the marked AZ directly, too much AZ will lead to the fragmentation of available resources", "It is unreachable for all floating ips.": "It is unreachable for all floating ips.", "It is unreachable for this floating ip.": "It is unreachable for this floating ip.", "Italy": "Italy", "Items in Cache": "Items in Cache", "Jamaica": "Jamaica", "Japan": "Japan", "Jordan": "Jordan", "Jump to Console": "Jump to Console", "Kampuchea": "Kampuchea", "Kazakhstan": "Kazakhstan", "Kenya": "Kenya", "Kernel ID": "Kernel ID", "Kernel Image": "Kernel Image", "Kernel Version": "Kernel Version", "Key": "Key", "Key Pair": "Key Pair", "Key Pairs": "Key Pairs", "Key Size (bits)": "Key Size (bits)", "Keypair": "Keypair", "Keypair Detail": "Keypair Detail", "Keypair Info": "Keypair Info", "Keystone Credentials": "Keystone Credentials", "Keystone token is expired.": "token has expired, please check whether the server time is correct and confirm whether the token is valid", "Kill": "Kill", "Kill Container": "Kill Container", "Kill Signal": "Kill Signal", "Killed": "Killed", "Kubernetes": "Kubernetes", "Kuwait": "Kuwait", "Kyrgyzstan": "Kyrgyzstan", "LB Algorithm": "LB Algorithm", "LEAST_CONNECTIONS": "Least Connections", "Labels": "Labels", "Lao People's Democratic Republic": "Lao People's Democratic Republic", "Large": "Large", "Large Screen": "Large Screen", "Large(Optimal performance)": "Large(Optimal performance)", "Last 2 Weeks": "Last 2 Weeks", "Last 24H Status": "Last 24H Status", "Last 7 Days": "Last 7 Days", "Last Day": "Last Day", "Last Hour": "Last Hour", "Last Updated": "Last Updated", "Last week alarm trend": "Last week alarm trend", "Latvia": "Latvia", "Leave Maintenance Mode": "Leave Maintenance Mode", "Lebanon": "Lebanon", "Left": "Left", "Lesotho": "Lesotho", "Liberia": "Liberia", "Libyan Arab Jamahiriya": "Libyan Arab Jam<PERSON>riya", "Liechtenstein": "Liechtenstein", "Lifetime": "Lifetime", "Lifetime Value": "Lifetime Value", "Listener": "Listener", "Listener Connection Limit": "Listener Connection Limit", "Listener Description": "Listener Description", "Listener Detail": "Listener <PERSON><PERSON>", "Listener Name": "Listener Name", "Listener Number": "Listener Number", "Listener Protocol": "Listener Protocol", "Listener Protocol Port": "Listener Protocol Port", "Listeners": "Listeners", "Lithuania": "Lithuania", "Live Migrate": "Live Migrate", "Live Migration At Destination": "Live Migration At Destination", "Load Balancer": "<PERSON><PERSON>r", "Load Balancer Detail": "Load Balancer <PERSON>ail", "Load Balancer Name": "Load Balancer Name", "Load Balancers": "Load Balancers", "Load Template from a file": "Load Template from a file", "Load from local files": "Load from local files", "LoadBalancers Instances": "LoadBalancers Instances", "Local": "Local", "Local Endpoint Group": "Local Endpoint Group", "Local Endpoint Group ID": "Local Endpoint Group ID", "Local Link Connection": "Local Link Connection", "Local Network": "Local Network", "Local SSD": "Local SSD", "Local Subnet": "Local Subnet", "Locality": "Locality", "Lock": "Lock", "Lock Instance": "Lock Instance", "Lock Status": "Lock Status", "Lock instance will lock the operations that have a direct impact on the operation of the instance, such as: shutdown, restart, delete, the mounting and unmounting of volume, etc. It does not involve the capacity expansion and change type of volume.": "Lock instance will lock the operations that have a direct impact on the operation of the instance, such as: shutdown, restart, delete, the mounting and unmounting of volume, etc. It does not involve the capacity expansion and change type of volume.", "Locked": "Locked", "Log": "Log", "Log Length": "Log Length", "Log in": "Log in", "Login Name": "Login Name", "Login Password": "Login Password", "Login Type": "Login Type", "Login Captcha Verification": "Login Captcha Verification", "Login Failure Check": "Login Failure Check", "Login Failure Count": "<PERSON>gin Failure Count", "Show UCCPS Button": "Show UCCPS Button", "Show Large Screen Button": "Show Large Screen Button", "Show Health Inspection Button": "Show Health Inspection Button", "Show New Health Inspection Button": "Show New Health Inspection Button", "Start Inspection": "Start Inspection", "Start Detection": "Start Detection", "Inspection Records": "Inspection Records", "Inspection Configuration": "Inspection Configuration", "Configure platform-wide health inspection work items to ensure normal operation of various health indicators of the platform": "Configure platform-wide health inspection work items to ensure normal operation of various health indicators of the platform", "HostOS Inspection Items": "HostOS Inspection Items", "Cloud Platform Inspection Items": "Cloud Platform Inspection Items", "GuestOS Inspection Items": "GuestOS Inspection Items", "Storage Inspection Items": "Storage Inspection Items", "HostOS Disk Usage Check": "HostOS Disk Usage Check", "HostOS CPU Usage Check": "HostOS CPU Usage Check", "HostOS Memory Usage Check": "HostOS Memory Usage Check", "HostOS Capacity Monitoring Check": "HostOS Capacity Monitoring Check", "Physical Network Card Monitoring Security Check": "Physical Network Card Monitoring Security Check", "Platform Virtual Machine Status Check": "Platform Virtual Machine Status Check", "CinderVolume Service": "CinderVolume Service", "NovaCompute Service": "NovaCompute Service", "NeutronOpenvswitch Service": "NeutronOpenvswitch Service", "Keepalived Service": "Keepalived Service", "NeutronDhcp Service": "NeutronDhcp Service", "NeutronL3 Service": "NeutronL3 Service", "Glance Service": "Glance Service", "Platform Status Security Check": "Platform Status Security Check", "Keystone Service": "Keystone Service", "GuestOS Usage Check": "GuestOS Usage Check", "GuestOS CPU Usage Check": "GuestOS CPU Usage Check", "Various Security Checks": "Various Security Checks", "Cluster Status": "Cluster Status", "Storage Status Security Check": "Storage Status Security Check", "Storage Capacity Status": "Storage Capacity Status", "PG Status": "PG Status", "OSD Status": "OSD Status", "Please select at least one inspection item": "Please select at least one inspection item", "Inspection started, please wait...": "Inspection started, please wait...", "Inspection completed successfully": "Inspection completed successfully", "Network Resource Detection": "Network Resource Detection", "Virtual Router Status": "Virtual Router Status", "Virtual Gateway Status": "Virtual Gateway Status", "Port Status": "Port Status", "Host Packet Loss Detection": "Host Packet Loss Detection", "Response Content Based Health Check": "Response Content Based Health Check", "HostOS巡检检测": "HostOS Inspection Detection", "HostOS磁盘利用率检查": "HostOS Disk Usage Check", "HostOS CPU利用率检查": "HostOS CPU Usage Check", "HostOS内存利用率检查": "HostOS Memory Usage Check", "HostOS容量监控检查": "HostOS Capacity Monitoring Check", "物理网卡监控安全检查": "Physical Network Card Monitoring Security Check", "云平台巡检项目": "Cloud Platform Inspection Items", "平台虚拟机状态检查": "Platform Virtual Machine Status Check", "CinderVolume服务": "CinderVolume Service", "NovaCompute服务": "NovaCompute Service", "NeutronOpenvswitch服务": "NeutronOpenvswitch Service", "Keepalived服务": "Keepalived Service", "NeutronDhcp服务": "NeutronDhcp Service", "NeutronL3服务": "NeutronL3 Service", "Glance服务": "Glance Service", "平台状态安全检查": "Platform Status Security Check", "Keystone服务": "Keystone Service", "GuestOS巡检检测": "GuestOS Inspection Detection", "GuestOS利用率检查": "GuestOS Usage Check", "GuestOS CPU利用率检查": "GuestOS CPU Usage Check", "各类安全检查": "Various Security Checks", "存储巡检项目": "Storage Inspection Items", "集群状态": "Cluster Status", "存储状态安全检查": "Storage Status Security Check", "存储容量状态": "Storage Capacity Status", "PG状态": "PG Status", "OSD状态": "OSD Status", "网络资源检测": "Network Resource Detection", "虚拟路由状态": "Virtual Router Status", "虚拟网关状态": "Virtual Gateway Status", "端口状态": "Port Status", "主机丢包检测": "Host Packet Loss Detection", "基于响应内容的健康检查": "Response Content Based Health Check", "HostOS资源检测": "HostOS Resource Detection", "HostOS分区利用率检测": "HostOS Partition Usage Detection", "HostOS CPU利用率检测": "HostOS CPU Usage Detection", "HostOS内存利用率检测": "HostOS Memory Usage Detection", "HostOS告警事件检测": "HostOS Alarm Event Detection", "物理网卡连通状态检测": "Physical Network Interface Connectivity Detection", "云平台资源检测": "Cloud Platform Resource Detection", "平台数据库状态检测": "Platform Database Status Detection", "Neutron服务": "Neutron Service", "NeutronServer服务": "NeutronServer Service", "Nova服务": "Nova Service", "Cinder服务": "Cinder Service", "平台HA状态检测": "Platform HA Status Detection", "Select All": "Select All", "Unselect All": "Unselect All", "Collapse All": "Collapse All", "Expand All": "Expand All", "Stop Detection": "Stop Detection", "Inspection stopped": "Inspection stopped", "Elapsed Time": "Elapsed Time", "Inspection Items": "Inspection Items", "GuestOS资源检测": "GuestOS Resource Detection", "GuestOS内存利用率检测": "GuestOS Memory Usage Detection", "GuestOS CPU利用率检测": "GuestOS CPU Usage Detection", "卷状态": "Volume Status", "存储资源检测": "Storage Resource Detection", "存储池状态检测": "Storage Pool Status Detection", "存储对象状态": "Storage Object Status", "We are inspecting your cloud platform, please wait": "We are inspecting your cloud platform, please wait", "Export Report": "Export Report", "Export Details": "Export Details", "Send Log": "Send Log", "Normal": "Normal", "Warning": "Warning", "Detection Item": "Detection Item", "Status": "Status", "Detection Result Details": "Detection Result Details", "Repair Suggestions": "Repair Suggestions", "Detection Results": "Detection Results", "Please select a detection item from the left to view details": "Please select a detection item from the left to view details", "Detection Result": "Detection Result", "Detailed Description": "Detailed Description", "Field": "Field", "Value": "Value", "Step": "Step", "Suggestion": "Suggestion", "Inspection Results": "Inspection Results", "View Inspection Results": "View Inspection Results", "Show Ticket Management Menu": "Show Ticket Management Menu", "Ticket Management": "Ticket Management", "My Tickets": "My Tickets", "Pending Tickets": "Pending Tickets", "Completed Tickets": "Completed Tickets", "Settings saved successfully!": "Setting<PERSON> saved successfully!", "Other Setting": "Other Setting", "Save": "Save", "Logs": "Logs", "Luxembourg": "Luxembourg", "MAC Address": "MAC Address", "MAC Learning State": "MAC Learning State", "MAPRFS": "MAPRFS", "MEM %": "MEM %", "MEM LIMIT (MiB)": "MEM LIMIT (MiB)", "MEM USAGE (MiB)": "MEM USAGE (MiB)", "MTU": "MTU", "Mac Address": "<PERSON>dress", "MacVTap": "MacVTap", "Macau": "Macau", "Madagascar": "Madagascar", "Mail Exchange Record": "Mail Exchange Record", "Maintained": "Maintained", "Maintenance": "Maintenance", "Malawi": "Malawi", "Malaysia": "Malaysia", "Maldives": "Maldives", "Mali": "Mali", "Malta": "Malta", "Manage Access": "Manage Access", "Manage Access Rule": "Manage Access Rule", "Manage Error": "Manage Error", "Manage Host": "Manage Host", "Manage Metadata": "Manage Metadata", "Manage Ports": "Manage Ports", "Manage QoS Spec": "Manage QoS Spec", "Manage Resource Types": "Manage Resource Types", "Manage Security Group": "Manage Security Group", "Manage Starting": "Manage Starting", "Manage State": "Manage State", "Manage User": "Manage User", "Manage User Group": "Manage User Group", "Manage host": "Manage host", "Manage user": "Manage user", "Manage user group": "Manage user group", "Manageable": "Manageable", "Management": "Management", "Management Reason": "Management Reason", "Mandatory for secondary zones. The servers to slave from to get DNS information.": "Mandatory for secondary zones. The servers to slave from to get DNS information.", "Manu": "<PERSON><PERSON>", "Manual input": "Manual input", "Manually Assigned Address": "Manually Assigned Address", "Manually Specify": "Manually Specify", "Marshall Islands": "Marshall Islands", "Martinique": "Martinique", "Master Node Addresses": "Master <PERSON>de Addresses", "Master Node Flavor": "Master <PERSON><PERSON>", "Master Node LB Enabled": "Master Node LB Enabled", "Masters": "Masters", "Mauritania": "Mauritania", "Mauritius": "Mauritius", "Max Avail": "<PERSON>", "Max BandWidth": "<PERSON>", "Max Burst": "<PERSON>", "Max Retries": "Max Retries", "Max Retry": "<PERSON>", "Max connect": "Max connect", "Maximum interval time for each health check response": "Maximum interval time for each health check response", "Maximum time to allow one check to run in seconds": "Maximum time to allow one check to run in seconds", "Mayotte": "Mayotte", "Mem": "<PERSON><PERSON>", "Member Count": "Member Count", "Member Detail": "Member Detail", "Member Num": "Member <PERSON><PERSON>", "Members": "Members", "Members of Each Group": "Members of Each Group", "Members of Each Server Group": "Members of Each Server Group", "Memory": "Memory", "Memory (GiB)": "Memory (GiB)", "Memory (MiB)": "Memory (MiB)", "Memory Optimized": "Memory Optimized", "Memory Page": "Memory Page", "Memory Page Size": "Memory Page <PERSON>", "Memory Usage": "Memory Usage", "Memory Usage(%)": "Memory Usage(%)", "Memory Usages (GiB)": "Memory Usages (GiB)", "Mesos": "Mesos", "Message": "Message", "Message Details": "Message Details", "Message Queue Service": "Message Queue Service", "Metadata": "<PERSON><PERSON><PERSON>", "Metadata Definitions": "Metadata Definitions", "Metadata Detail": "<PERSON><PERSON><PERSON>", "Mexico": "Mexico", "Micronesia": "Micronesia", "Migrate": "Migrate", "Migrate Volume": "Migrate Volume", "Migrate volume": "Migrate volume", "Migrating": "Migrating", "Migrating To": "Migrating To", "Min Memory": "Min <PERSON>", "Min Memory (GiB)": "Min Memory (GiB)", "Min System Disk": "Min System Disk", "Min System Disk (GiB)": "Min System Disk (GiB)", "Min size": "Min size", "Min. Disk": "<PERSON><PERSON>", "Min. RAM": "Min. <PERSON>", "Minimum value is 68 for IPv4, and 1280 for IPv6.": "Minimum value is 68 for IPv4, and 1280 for IPv6.", "Miscellaneous": "Miscellaneous", "Missing IP Address": "Missing IP Address", "Missing Port": "Missing Port", "Missing Subnet": "Missing Subnet", "Missing Weight": "Missing Weight", "Modification Times": "Modification Times", "Modify Instance Tags": "Modify Instance Tags", "Modify Project Tags": "Modify Project Tags", "Modify QoS": "Modify QoS", "Moldova": "Moldova", "Monaco": "Monaco", "Mongolia": "Mongolia", "Monitor Center": "Monitor Center", "Monitor Overview": "Monitor Overview", "Montenegro": "Montenegro", "Montserrat": "Montserrat", "More": "More", "More Actions": "More Actions", "More than one label is required, such as: \"example.org.\"": "More than one label is required, such as: \"example.org.\"", "Morocco": "Morocco", "Mount ISO": "Mount ISO", "Mount snapshot support": "Mount snapshot support", "Mozambique": "Mozambique", "Multiple filter tags are separated by enter": "Multiple filter tags are separated by enter", "My Role": "My Role", "MySQL Actions": "MySQL Actions", "Myanmar": "Myanmar", "N/A": "N/A", "NET I/O(B)": "NET I/O(B)", "NFS": "NFS", "NOOP": "NOOP", "NUMA Node": "NUMA Node", "NUMA Node Count": "NUMA Node Count", "NUMA Nodes": "NUMA Nodes", "Name": "Name", "Name Server": "Name Server", "Name can not be duplicated": "Name can not be duplicated", "Name or ID og the container image": "Name or ID og the container image", "Namespace": "Namespace", "Namibia": "Namibia", "Nauru": "Nauru", "Nepal": "Nepal", "Netherlands": "Netherlands", "Netherlands Antilles": "Netherlands Antilles", "Network": "Network", "Network Attaching": "Network Attaching", "Network Config": "Network Config", "Network Detaching": "Network Detaching", "Network Detail": "Network Detail", "Network Driver": "Network Driver", "Network Dropped Packets": "Network Dropped Packets", "Network Errors": "Network Errors", "Network ID": "Network ID", "Network ID/Name": "Network ID/Name", "Network Info": "Network Info", "Network Interface": "Network Interface", "Network Line": "Network Line", "Network Name": "Network Name", "Network Service": "Network Service", "Network Setting": "Network Setting", "Network Traffic": "Network Traffic", "Network Type": "Network Type", "Network topology page": "Network topology page", "Networking": "Networking", "Networking *": "Networking *", "Networks": "Networks", "Neutron Agent Detail": "Neutron Agent Detail", "Neutron Agents": "Neutron Agents", "Neutron Net": "Neutron Net", "Neutron Service": "Neutron Service", "Neutron Subnet": "Neutron Subnet", "New": "New", "New Availability Zone": "New Availability Zone", "New Caledonia": "New Caledonia", "New Status": "New Status", "New Tag": "New Tag", "New Volume": "New Volume", "New Zealand": "New Zealand", "Next": "Next", "Next Hop": "Next Hop", "Nicaragua": "Nicaragua", "Niger": "Niger", "Nigeria": "Nigeria", "No": "No", "No - Do not create a new system disk": "No - Do not create a new system disk", "No Console": "No Console", "No Logs...": "No Logs...", "No Monitor": "No Monitor", "No Outputs": "No Outputs", "No Proxy": "No Proxy", "No Raid": "No Raid", "No State": "No State", "No Task": "No Task", "No Vender": "No Vender", "No default pool set": "No default pool set", "Node": "Node", "Node Addresses": "Node Addresses", "Node Driver": "Node Driver", "Node Flavor": "Node Flavor", "Node ID/Name": "Node ID/Name", "Node Info": "Node Info", "Node Name": "Node Name", "Node Spec": "Node Spec", "Nodes": "Nodes", "Nodes To Remove": "Nodes To Remove", "Norfolk Island": "Norfolk Island", "North Korea": "North Korea", "Northern Mariana Islands": "Northern Mariana Islands", "Norway": "Norway", "Not Implemented (code: 501) ": "Not Implemented (code: 501) ", "Not Open": "Not Open", "Not dealt with for the time being": "Not dealt with for the time being", "Not deleted with the instance": "Not deleted with the instance", "Not locked": "Not locked", "Not select": "Not select", "Not yet bound": "Not yet bound", "Not yet selected": "Not yet selected", "Note that when using a share type with the driver_handles_share_servers extra spec as False, you should not provide a share network.": "Note that when using a share type with the driver_handles_share_servers extra spec as False, you should not provide a share network.", "Note: Are you sure you need to modify the volume type?": "Note: Are you sure you need to modify the volume type?", "Note: Please consider the container name carefully since it couldn't be changed after created.": "Note: Please consider the container name carefully since it couldn't be changed after created.", "Note: The security group you use will act on all virtual adapters of the instance.": "Note: The security group you use will act on all virtual adapters of the instance.", "Notification Detail": "Notification Detail", "Notifications": "Notifications", "Nova Service": "Nova Service", "Number of GPU": "Number of GPU", "Number of IPs used by all projects": "Number of IPs used by all projects", "Number of Master Nodes": "Number of Master Nodes", "Number of Nodes": "Number of Nodes", "Number of Ports": "Number of Ports", "Number of Usb Controller": "Number of Usb Controller", "OK": "OK", "OS": "OS", "OS Admin": "OS Admin", "OS Disk": "OS Disk", "OS Type": "OS Type", "OS Version": "OS Version", "OSDs": "OSDs", "OSPF": "OSPF", "Object": "Object", "Object Count": "Object Count", "Object Count ": "Object Count ", "Object ID": "Object ID", "Object ID/Name": "Object ID/Name", "Object Name": "Object Name", "Object Storage": "Object Storage", "Object Type": "Object Type", "Off": "Off", "Offline": "Offline", "Oman": "Oman", "On": "On", "On Maintenance": "On Maintenance", "On failure": "On failure", "One entry per line(e.g. ***************)": "One entry per line(e.g. ***************)", "One entry per line(e.g. {ip})": "One entry per line(e.g. {ip})", "One-way authentication": "One-way authentication", "Online": "Online", "Online Resize": "Online Resize", "Only a MAC address or an OpenFlow based datapath_id of the switch are accepted in this field": "Only a MAC address or an OpenFlow based datapath_id of the switch are accepted in this field", "Only libvirt driver is supported.": "Only libvirt driver is supported.", "Only subnets that are already connected to the router can be selected.": "Only subnets that are already connected to the router can be selected.", "Open External Gateway": "Open External Gateway", "OpenID Connect": "OpenID Connect", "Operating Status": "Operating Status", "Operating System": "Operating System", "Operation Center": "Operation Center", "Operation Name": "Operation Name", "Operation Time": "Operation Time", "Optimized Parameters": "Optimized Parameters", "Optional list": "Optional list", "Options": "Options", "Orchestration": "Orchestration", "Orchestration Services": "Orchestration Services", "Orchestration information": "Orchestration information", "Origin File Name": "Origin File Name", "Original Password": "Original Password", "Other Protocol": "Other Protocol", "Other Service": "Other Service", "Other Services": "Other Services", "Others": "Others", "Out Cluster": "Out Cluster", "Out of Sync": "Out of Sync", "Outputs": "Outputs", "Overlapping allocation pools: {pools}": "Overlapping allocation pools: {pools}", "Overlay": "Overlay", "Overlay2": "Overlay2", "Overview": "Overview", "Owned Network": "Owned Network", "Owned Network ID": "Owned Network ID", "Owned Network ID/Name": "Owned Network ID/Name", "Owned Project": "Owned Project", "Owned Subnet": "Owned Subnet", "Owner": "Owner", "Ownership of a volume can be transferred from one project to another. The transfer process of the volume needs to perform the transfer operation in the original owner's project, and complete the \"accept\" operation in the receiver's project.": "Ownership of a volume can be transferred from one project to another. The transfer process of the volume needs to perform the transfer operation in the original owner's project, and complete the \"accept\" operation in the receiver's project.", "PEM encoding": "PEM encoding", "PFS": "PFS", "PG Count": "PG Count", "PGM": "PGM", "PING": "PING", "PTR Domain Name": "PTR Domain Name", "PXE": "PXE", "PXE Enabled": "PXE Enabled", "Pakistan": "Pakistan", "Palau": "<PERSON><PERSON>", "Palestine": "Palestine", "Panama": "Panama", "Papua New Guinea": "Papua New Guinea", "Paraguay": "Paraguay", "Parameter": "Parameter", "Params Setting": "Params Setting", "Password": "Password", "Password Type": "Password Type", "Password changed successfully, please log in again.": "Password changed successfully, please log in again.", "Password must be the same with confirm password.": "Password must be the same with confirm password.", "Paste": "Paste", "Paste File": "Paste File", "Path": "Path", "Pause": "Pause", "Pause Container": "Pause Container", "Pause Instance": "Pause Instance", "Paused": "Paused", "Pausing": "Pausing", "Payload": "Payload", "Peer": "<PERSON><PERSON>", "Peer Address": "Peer Address", "Peer Cidrs": "<PERSON><PERSON>", "Peer Endpoint Group": "Peer Endpoint Group", "Peer Endpoint Group ID": "Peer Endpoint Group ID", "Peer Gateway Public Address": "Peer Gateway Public Address", "Peer ID": "Peer ID", "Peer Network": "Peer Network", "Peer Network Segment": "Peer Network Segment", "Peer gateway public address for the IPsec site connection": "Peer gateway public address for the IPsec site connection", "Pending": "Pending", "Pending Create": "Pending Create", "Pending Delete": "Pending Delete", "Pending Update": "Pending Update", "Perform a consistent hash operation on the source IP address of the request to obtain a specific value. At the same time, the back-end server is numbered, and the request is distributed to the server with the corresponding number according to the calculation result. This can enable load distribution of visits from different source IPs, and at the same time enable requests from the same client IP to always be dispatched to a specific server. This method is suitable for load balancing TCP protocol without cookie function.": "Perform a consistent hash operation on the source IP address of the request to obtain a specific value. At the same time, the back-end server is numbered, and the request is distributed to the server with the corresponding number according to the calculation result. This can enable load distribution of visits from different source IPs, and at the same time enable requests from the same client IP to always be dispatched to a specific server. This method is suitable for load balancing TCP protocol without cookie function.", "Permanent": "Permanent", "Persistent": "Persistent", "Peru": "Peru", "Phase1 Negotiation Mode": "Phase1 Negotiation Mode", "Philippines": "Philippines", "Phone": "Phone", "Physical CPU Usage": "Physical CPU Usage", "Physical Network": "Physical Network", "Physical Node": "Physical Node", "Physical Nodes": "Physical Nodes", "Physical Storage Usage": "Physical Storage Usage", "Pitcairn": "Pitcairn", "Platform Info": "Platform Info", "Please confirm your password!": "Please confirm your password!", "Please enter JSON in the correct format!": "Please enter JSON in the correct format!", "Please enter URL!": "Please enter URL!", "Please enter a correct certificate content, format is refer to the left tip!": "Please enter a correct certificate content, format is refer to the left tip!", "Please enter a correct domain, format is refer to the left tip!": "Please enter a correct domain, format is refer to the left tip!", "Please enter a correct private key, format is refer to the left tip!": "Please enter a correct private key, format is refer to the left tip!", "Please enter a file link starting with \"http://\" or \"https://\"!": "Please enter a file link starting with \"http://\" or \"https://\"!", "Please enter a memory page size, such as: 1024, 1024MiB": "Please enter a memory page size, such as: 1024, 1024MiB", "Please enter a valid ASCII code": "Please enter a valid ASCII code", "Please enter a valid Email Address!": "Please enter a valid Email Address!", "Please enter a valid IPv4 value.": "Please enter a valid IPv4 value.", "Please enter a valid IPv6 value.": "Please enter a valid IPv6 value.", "Please enter a valid Phone Number": "Please enter a valid Phone Number", "Please enter complete key value!": "Please enter complete key value!", "Please enter right format custom trait!": "Please enter right format custom trait!", "Please enter right format key value!": "Please enter right format key value!", "Please enter right format memory page value!": "Please enter right format memory page value!", "Please enter right format trait!": "Please enter right format trait!", "Please enter the correct id": "Please enter the correct id", "Please enter the server id to be reduced, and separate different id with \",\"": "Please enter the server id to be reduced, and separate different id with \",\"", "Please fill in the peer network segment and subnet mask of CIDR format, the written subnets should be under the same router, one per line.": "Please fill in the peer network segment and subnet mask of CIDR format, the written subnets should be under the same router, one per line.", "Please input": "Please input", "Please input <username> or <username>@<domain name>!": "Please input <username> or <username>@<domain name>!", "Please input ICMP code(0-255)": "Please input ICMP code(0-255)", "Please input ICMP type(0-255)": "Please input ICMP type(0-255)", "Please input IPv4 or IPv6 cidr": "Please input IPv4 or IPv6 cidr", "Please input IPv4 or IPv6 cidr, (e.g. ***********/24, 2001:DB8::/48)": "Please input IPv4 or IPv6 cidr, (e.g. ***********/24, 2001:DB8::/48)", "Please input a number": "Please input a number", "Please input a parameter": "Please input a parameter", "Please input a valid ip!": "Please input a valid ip!", "Please input a value": "Please input a value", "Please input at least 2 characters.": "Please input at least 2 characters.", "Please input at least one record": "Please input at least one record", "Please input auth key": "Please input auth key", "Please input cipher": "Please input cipher", "Please input cluster name": "Please input cluster name", "Please input cluster template name": "Please input cluster template name", "Please input complete data": "Please input complete data", "Please input container name": "Please input container name", "Please input file name": "Please input file name", "Please input image": "Please input image", "Please input ip address": "Please input ip address", "Please input ipv4": "Please input ipv4", "Please input ipv6": "Please input ipv6", "Please input key": "Please input key", "Please input key and value": "Please input key and value", "Please input key size": "Please input key size", "Please input metadata": "Please input metadata", "Please input name": "Please input name", "Please input or load Template from a file": "Please input or load Template from a file", "Please input port and protocol": "Please input port and protocol", "Please input prefix": "Please input prefix", "Please input protocol number if it absent in select list.": "Please input protocol number if it absent in select list.", "Please input provider": "Please input provider", "Please input snapshot name": "Please input snapshot name", "Please input the correct format:  <username> or <username>@<domain name>.": "Please input the correct format:  <username> or <username>@<domain name>.", "Please input transfer id": "Please input transfer id", "Please input user name": "Please input user name", "Please input value": "Please input value", "Please input your Password!": "Please input your Password!", "Please input your Username!": "Please input your Username!", "Please input your current password!": "Please input your current password!", "Please input your password!": "Please input your password!", "Please input {label}": "Please input {label}", "Please input {label}!": "Please input {label}!", "Please make sure this IP address be available to avoid creating VM failure.": "Please make sure this IP address be available to avoid creating VM failure.", "Please make sure this IP address be available.": "Please make sure this IP address be available.", "Please note that when deleting a domain, all projects, users, and user groups under the domain will be deleted directly!": "Please note that when deleting a domain, all projects, users, and user groups under the domain will be deleted directly!", "Please reasonably plan the network and subnet to which the virtual network card belongs.": "Please reasonably plan the network and subnet to which the virtual network card belongs.", "Please save your token properly and it will be valid for {left}.": "Please save your token properly and it will be valid for {left}.", "Please select": "Please select", "Please select a file": "Please select a file", "Please select a file with the suffix {types}": "Please select a file with the suffix {types}", "Please select a network!": "Please select a network!", "Please select a parameter": "Please select a parameter", "Please select a subnet!": "Please select a subnet!", "Please select a type!": "Please select a type!", "Please select availability zone": "Please select availability zone", "Please select image driver": "Please select image driver", "Please select item!": "Please select item!", "Please select login type!": "Please select login type!", "Please select policy": "Please select policy", "Please select source": "Please select source", "Please select type": "Please select type", "Please select volume type": "Please select volume type", "Please select your Region!": "Please select your Region!", "Please select {label}!": "Please select {label}!", "Please select {name} first": "Please select {name} first", "Please select: {name} or an image file that is the same as it": "Please select: {name} or an image file that is the same as it", "Please set CPU && Ram first.": "Please set CPU && Ram first.", "Please set MUNA": "Please set MUNA", "Please set a size no less than {minSize} GiB!": "Please set a size no less than {minSize} GiB!", "Please set at least one role!": "Please set at least one role!", "Please set the system disk size!": "Please set the system disk size!", "Please upload files smaller than { size }GiB on the page. It is recommended to upload files over { size }GiB using API.": "Please upload files smaller than { size }GiB on the page. It is recommended to upload files over { size }GiB using API.", "Pointer Record": "Pointer Record", "Poland": "Poland", "Policy": "Policy", "Policy Detail": "Policy Detail", "Policy Edit": "Policy Edit", "Policy Name": "Policy Name", "Policy Rules": "Policy Rules", "Pool Algorithm": "Pool Algorithm", "Pool Description": "Pool Description", "Pool Detail": "Pool Detail", "Pool ID": "Pool ID", "Pool Info": "Pool Info", "Pool Name": "Pool Name", "Pool Protocol": "Pool Protocol", "Pools": "Pools", "Port": "Port", "Port Count": "Port Count", "Port Detail": "Port Detail", "Port Forwardings": "Port Forwardings", "Port Group": "Port Group", "Port Groups": "Port Groups", "Port ID": "Port ID", "Port Info": "Port Info", "Port Range": "Port Range", "Port Security": "Port Security", "Port Security Enabled": "Port Security Enabled", "Port Type": "Port Type", "Ports": "Ports", "Ports are either single values or ranges": "Ports are either single values or ranges", "Ports provide extra communication channels to your containers. You can select ports instead of networks or a mix of both, If the terminal port and the network are selected at the same time, note that the terminal port is not a terminal port of the selected network, and the container under the same network will only be assigned one IP address (The port executes its own security group rules by default).": "Ports provide extra communication channels to your containers. You can select ports instead of networks or a mix of both, If the terminal port and the network are selected at the same time, note that the terminal port is not a terminal port of the selected network, and the container under the same network will only be assigned one IP address (The port executes its own security group rules by default).", "Ports provide extra communication channels to your instances. You can select ports instead of networks or a mix of both (The port executes its own security group rules by default).": "Ports provide extra communication channels to your instances. You can select ports instead of networks or a mix of both (The port executes its own security group rules by default).", "Portugal": "Portugal", "Power Off": "Power Off", "Power On": "Power On", "Power State": "Power State", "Powering Off": "Powering Off", "Powering On": "Powering On", "Pre Live Migration": "Pre Live Migration", "Pre-Shared Key must be the same with Confirm Shared Key.": "Pre-Shared Key must be the same with Confirm Shared Key.", "Pre-Shared Key(PSK) String": "Pre-Shared Key(PSK) String", "Prefer": "Prefer", "Prefer(Thread siblings are preferred)": "Prefer(Thread siblings are preferred)", "Preferred": "Preferred", "Prefix": "Prefix", "Prep Resize": "Prep Resize", "Prepare Template": "Prepare Template", "Previous": "Previous", "Primary": "Primary", "Primary is controlled by Designate, Secondary zones are slaved from another DNS Server.": "Primary is controlled by Designate, Secondary zones are slaved from another DNS Server.", "Private": "Private", "Private Key": "Private Key", "Profile": "Profile", "Progress": "Progress", "Project": "Project", "Project Console": "Project Console", "Project Detail": "Project Detail", "Project ID": "Project ID", "Project ID/Name": "Project ID/Name", "Project Name": "Project Name", "Project Num": "Project Num", "Project Quota": "Project Quota", "Project Range": "Project Range", "Project Scope": "Project Scope", "Project Scope (Project Name: Role Names)": "Project Scope (Project Name: Role Names)", "Project User Groups": "Project User Groups", "Project Users": "Project Users", "Projects": "Projects", "Promote": "Promote", "Properties": "Properties", "Protected": "Protected", "Protocol": "Protocol", "Protocol Type": "Protocol Type", "Provider": "Provider", "Provider Network Type": "Provider Network Type", "Provider Physical Network": "Provider Physical Network", "Provision State": "Provision State", "Provisioning Status": "Provisioning Status", "Public": "Public", "Public Access": "Public Access", "Public Address": "Public Address", "Public Images": "Public Images", "Public Key": "Public Key", "Published In": "Published In", "Published Out": "Published Out", "Puerto Rico": "Puerto Rico", "QCOW2 - QEMU image format": "QCOW2 - QEMU image format", "Qatar": "Qatar", "QoS Bandwidth Egress Limit": "QoS Bandwidth Egress Limit", "QoS Bandwidth Ingress Limit": "QoS Bandwidth Ingress Limit", "QoS Bandwidth Limit": "QoS Bandwidth Limit", "QoS Detail": "QoS Detail", "QoS Policies": "QoS Policies", "QoS Policy": "QoS Policy", "QoS Policy Detail": "QoS Policy Detail", "QoS Policy ID": "QoS Policy ID", "QoS Policy ID/Name": "QoS Policy ID/Name", "QoS Spec": "QoS Spec", "QoS Spec ID": "QoS Spec ID", "QoS Specs": "QoS Specs", "QoS policies": "QoS policies", "Qos Policy": "Qos Policy", "Queued": "Queued", "Queued To Apply": "Queued To Apply", "Queued To Deny": "Queued To Deny", "Quota": "<PERSON><PERSON><PERSON>", "Quota Overview": "Quota Overview", "Quota exceeded": "<PERSON><PERSON><PERSON> exceeded", "Quota is not enough for extend share.": "Quota is not enough for extend share.", "Quota is not enough for extend volume.": "Quota is not enough for extend volume.", "Quota of key pair means: the number of allowed key pairs for each user.": "Quota of key pair means: the number of allowed key pairs for each user.", "Quota: Insufficient quota to create resources, please adjust resource quantity or quota(left { quota }, input { input }).": "Quota: Insufficient quota to create resources, please adjust resource quantity or quota(left { quota }, input { input }).", "Quota: Insufficient { name } quota to create resources, please adjust resource quantity or quota(left { left }, input { input }).": "Quota: Insufficient { name } quota to create resources, please adjust resource quantity or quota(left { left }, input { input }).", "Quota: Insufficient { name } quota to create resources.": "Quota: Insufficient { name } quota to create resources.", "Quota: Project quotas sufficient resources can be created": "Quota: Project quotas sufficient resources can be created", "RAM": "RAM", "RAM (MiB)": "RAM (MiB)", "RAW - Raw disk image format": "RAW - Raw disk image format", "RBAC Policies": "RBAC Policies", "RBAC Policy Detail": "RBAC Policy Detail", "REJECT": "REJECT", "RESTORE COMPLETE": "RESTORE COMPLETE", "RESUME COMPLETE": "RESUME COMPLETE", "RESUME FAILED": "RESUME FAILED", "ROLLBACK COMPLETE": "ROLLBACK COMPLETE", "ROLLBACK FAILED": "ROLLBACK FAILED", "ROLLBACK IN PROGRESS": "ROLLBACK IN PROGRESS", "ROUND_ROBIN": "Round Robin", "RSVP": "RSVP", "Raid Interface": "Raid Interface", "Ram Size (GiB)": "<PERSON> (GiB)", "Ram value is { ram }, NUMA RAM value is { totalRam }, need to be equal. ": "Ram value is { ram }, NUMA RAM value is { totalRam }, need to be equal. ", "Ramdisk ID": "Ramdisk ID", "Ramdisk Image": "Ramdisk Image", "Rbac Policy": "Rbac Policy", "Read and write": "Read and write", "Read only": "Read only", "Real Name": "Real Name", "Reason": "Reason", "Reason: ": "Reason: ", "Reboot": "Reboot", "Reboot Container": "Reboot Container", "Reboot Database Instance": "Reboot Database Instance", "Reboot Instance": "Reboot Instance", "Rebooting": "Rebooting", "Rebuild": "Rebuild", "Rebuild Block Device Mapping": "Rebuild Block Device Mapping", "Rebuild Container": "Rebuild Container", "Rebuild Instance": "Rebuild Instance", "Rebuild Spawning": "Rebuild Spawning", "Rebuilding": "Rebuilding", "Rebuilt": "Rebuilt", "Recently a day": "Recently a day", "Record Sets": "Record Sets", "Records": "Records", "Recordset Detail": "Recordset Detail", "Recordsets Detail": "Recordsets Detail", "Recover": "Recover", "Recovering": "Recovering", "Recovery Method": "Recovery Method", "Recycle Bin": "Recycle Bin", "Region": "Region", "Registry Enabled": "Registry Enabled", "Related Policy": "Related Policy", "Related Resources": "Related Resources", "Release": "Release", "Release Fixed IP": "Release Fixed IP", "Remote Group Id": "Remote Group Id", "Remote IP Prefix": "Remote IP Prefix", "Remote Security Group": "Remote Security Group", "Remote Type": "Remote Type", "Remove": "Remove", "Remove Default Project": "Remove Default Project", "Remove Network": "Remove Network", "Remove Router": "Remove Router", "Remove Rule": "Remove Rule", "Remove default project for user": "Remove default project for user", "Rename": "<PERSON><PERSON>", "Rename is to copy the current file to the new file address and delete the current file, which will affect the creation time of the file.": "Rename is to copy the current file to the new file address and delete the current file, which will affect the creation time of the file.", "Replication Change": "Replication Change", "Report Count": "Report Count", "Republic of the Congo": "Republic of the Congo", "Request ID": "Request ID", "Require": "Require", "Require(Need multithreading)": "Require(Need multithreading)", "Required Data Disk": "Required Data Disk", "Rescue": "Rescue", "Rescued": "Rescued", "Rescuing": "Rescuing", "Reserved": "Reserved", "Reset Status": "Reset Status", "Reset To Initial Value": "Reset To Initial Value", "Reset failed, please retry": "Reset failed, please retry", "Resize": "Resize", "Resize Cluster": "Resize Cluster", "Resize Instance": "Resize Instance", "Resize Volume": "Resize Volume", "Resized": "Resized", "Resizing or Migrating": "Resizing or Migrating", "Resource": "Resource", "Resource Class": "Resource Class", "Resource Class Properties": "Resource Class Properties", "Resource Id": "Resource Id", "Resource Not Found": "Resource Not Found", "Resource Pool": "Resource Pool", "Resource Status": "Resource Status", "Resource Status Reason": "Resource Status Reason", "Resource Type": "Resource Type", "Resource Types": "Resource Types", "Resources Synced": "Resources Synced", "Resource Monitor": "Resource Monitor", "Restart": "<PERSON><PERSON>", "Restart Container": "<PERSON><PERSON> Container", "Restart Database Service": "Restart Database Service", "Restarting": "Restarting", "Restore Backup": "Restore Backup", "Restore From Snapshot": "Restore From Snapshot", "Restore backup": "Restore backup", "Restore from snapshot": "Restore from snapshot", "Restoring": "Restoring", "Restoring Backup": "Restoring Backup", "Restricted": "Restricted", "Restricted Situation": "Restricted Situation", "Resume": "Resume", "Resume Complete": "Resume Complete", "Resume Failed": "Resume Failed", "Resume In Progress": "Resume In Progress", "Resume Instance": "Resume Instance", "Resuming": "Resuming", "Retry times for restart on failure policy": "Retry times for restart on failure policy", "Retyping": "Retyping", "Reunion": "Reunion", "Reverse DNS Detail": "Reverse DNS Detail", "Reverse Detail": "Reverse Detail", "Reverse Dns": "Reverse Dns", "Revert Resize or Migrate": "Revert Resize or Migrate", "Revert Resize/Migrate": "<PERSON><PERSON>/<PERSON>", "Reverting": "Reverting", "Reverting Error": "Reverting E<PERSON>r", "Reverting Resize or Migrate": "Reverting Resize or Migrate", "Role": "Role", "Role Detail": "Role Detail", "Role Name": "Role Name", "Roles": "Roles", "Rollback Complete": "Rollback Complete", "Rollback Failed": "Rollback Failed", "Rollback In Progress": "Rollback In Progress", "Romania": "Romania", "Root Disk": "Root Disk", "Root Disk (GiB)": "Root Disk (GiB)", "Root directory": "Root directory", "Router": "Router", "Router Advertisements Mode": "Router Advertisements Mode", "Router Detail": "Router Detail", "Router External": "Router External", "Router ID": "Router ID", "Router Port": "Router Port", "Routers": "Routers", "Rule": "Rule", "Rule Action": "Rule Action", "Rule Detail": "Rule Detail", "Rule Edit": "Rule Edit", "Rule Numbers": "Rule Numbers", "Rules": "Rules", "Rules Number": "Rules Number", "Running": "Running", "Running Threads": "Running Threads", "Running Time": "Running Time", "Runtime": "Runtime", "Russia": "Russia", "Rwanda": "Rwanda", "SCTP": "SCTP", "SNAPSHOT COMPLETE": "SNAPSHOT COMPLETE", "SNAT Enabled": "SNAT Enabled", "SNI Certificate": "SNI Certificate", "SNI Enabled": "SNI Enabled", "SOURCE_IP": "Source IP", "SSH Public Key Fingerprint": "SSH Public Key Fingerprint", "SSL Parsing Method": "SSL Parsing Method", "Saint Vincent and the Grenadines": "Saint Vincent and the Grenadines", "Same subnet with LB": "Same subnet with LB", "Samoa": "Samoa", "San Marino": "San Marino", "Sao Tome and Principe": "Sao Tome and Principe", "Saudi Arabia": "Saudi Arabia", "Saving": "Saving", "Scheduler Hints": "Scheduler Hints", "Scheduling": "Scheduling", "Search": "Search", "Sec for DPD delay, > 0": "Sec for DPD delay, > 0", "Sec for DPD timeout, > 0 & > DPD Interval": "Sec for DPD timeout, > 0 & > DPD Interval", "Secondary": "Secondary", "Security Group": "Security Group", "Security Group Detail": "Security Group Detail", "Security Group Info": "Security Group Info", "Security Group Num:": "Security Group Num:", "Security Group Rule": "Security Group Rule", "Security Group Rules": "Security Group Rules", "Security Groups": "Security Groups", "Security Groups Adding": "Security Groups Adding", "Security Groups Removing": "Security Groups Removing", "Security Info": "Security Info", "Segment Detail": "Segment Detail", "Segment ID": "Segment ID", "Segment Name": "Segment Name", "Segmentation ID": "Segmentation ID", "Segmentation Id": "Segmentation Id", "Segments": "Segments", "Select File": "Select File", "Select Project": "Select Project", "Select Project Role": "Select Project Role", "Select User Group": "Select User Group", "Select Volume Snapshot": "Select Volume Snapshot", "Select a QoS Policy": "Select a QoS Policy", "Select a login type": "Select a login type", "Select a network": "Select a network", "Select a project": "Select a project", "Select a region": "Select a region", "Select an object type": "Select an object type", "Selected": "Selected", "Selected Members": "Selected Members", "Selected list": "Selected list", "Sender Policy Framework": "Sender Policy Framework", "Senegal": "Senegal", "Serbia": "Serbia", "Serial": "Serial", "Server Certificate": "Server Certificate", "Server Certificates": "Server Certificates", "Server Group": "Server Group", "Server Group Detail": "Server Group Detail", "Server Group Member": "Server Group Member", "Server Groups": "Server Groups", "Server Status": "Server Status", "Server Type": "Server Type", "Service": "Service", "Service List": "Service List", "Service Locator": "Service Locator", "Service Port ID": "Service Port ID", "Service State": "Service State", "Service Status": "Service Status", "Service Status Updated": "Service Status Updated", "Service Type": "Service Type", "Service Unavailable (code: 503) ": "Service Unavailable (code: 503) ", "Services": "Services", "Set": "Set", "Set Admin Password": "Set Admin Password", "Set Boot Device": "Set Boot Device", "Set Default Project": "Set Default Project", "Set Domain Name PTR": "Set Domain Name PTR", "Set IP": "Set IP", "Set default project for user": "Set default project for user", "Seychelles": "Seychelles", "Share": "Share", "Share Capacity (GiB)": "Share Capacity (GiB)", "Share Detail": "Share Detail", "Share File Storage": "Share File Storage", "Share Group": "Share Group", "Share Group Detail": "Share Group Detail", "Share Group Type": "Share Group Type", "Share Group Type Detail": "Share Group Type Detail", "Share Group Types": "Share Group Types", "Share Groups": "Share Groups", "Share Id": "Share Id", "Share Instance": "Share Instance", "Share Instance Detail": "Share Instance Detail", "Share Instances": "Share Instances", "Share Network": "Share Network", "Share Network Detail": "Share Network Detail", "Share Network Subnet": "Share Network Subnet", "Share Network Subnets": "Share Network Subnets", "Share Networks": "Share Networks", "Share Protocol": "Share Protocol", "Share Replica ID": "Share Replica ID", "Share Server": "Share Server", "Share Server Detail": "Share Server Detail", "Share Servers": "Share Servers", "Share Type": "Share Type", "Share Type Detail": "Share Type Detail", "Share Type ID": "Share Type ID", "Share Type Name": "Share Type Name", "Share Types": "Share Types", "Shared": "Shared", "Shared Images": "Shared Images", "Shared Network": "Shared Network", "Shared Networks": "Shared Networks", "Shared QoS Policies": "Shared QoS Policies", "Shared QoS Policy": "Shared QoS Policy", "Shared policy only can insert shared rules.": "Shared policy only can insert shared rules.", "Shares": "Shares", "Shelve": "Shelve", "Shelve Instance": "Shelve Instance", "Shelved": "Shelved", "Shelved Offloaded": "Shelved Offloaded", "Shelving": "Shelving", "Shelving Image Pending Upload": "Shelving Image Pending Upload", "Shelving Image Uploading": "Shelving Image Uploading", "Shelving Offloading": "Shelving Offloading", "Show All Domain": "Show All Domain", "Show Instance": "Show Instance", "Show all Data": "Show all Data", "Shrinking": "Shrinking", "Shrinking Error": "Shrinking Error", "Shrinking Possible Data Loss Error": "Shrinking Possible Data Loss Error", "Shut Down": "Shut Down", "Shut Off": "Shut Off", "Shutoff": "Shutoff", "Sierra Leone": "Sierra Leone", "Sign Out": "Sign Out", "Sign up": "Sign up", "Signal to send to the container: integer or string like SIGINT. When not set, SIGKILL is set as default value and the container will exit. The supported signals varies between platform. Besides, you can omit \"SIG\" prefix.": "Signal to send to the container: integer or string like SIGIN<PERSON>. When not set, SIGKILL is set as default value and the container will exit. The supported signals varies between platform. Besides, you can omit \"SIG\" prefix.", "Singapore": "Singapore", "Size": "Size", "Size (GiB)": "Size (GiB)", "Slovakia (Slovak Republic)": "Slovakia (Slovak Republic)", "Slovenia": "Slovenia", "Slow Query": "Slow Query", "Small": "Small", "Small(Not recommended)": "Small(Not recommended)", "Smart Scheduling": "<PERSON> Scheduling", "Snapshot Complete": "Snapshot Complete", "Snapshot Failed": "Snapshot Failed", "Snapshot In Progress": "Snapshot In Progress", "Snapshot Instance": "Snapshot Instance", "Snapshot Source": "Snapshot Source", "Snapshots can be converted into volume and used to create an instance from the volume.": "Snapshots can be converted into volume and used to create an instance from the volume.", "Snapshotting": "Snapshotting", "Soft Delete Instance": "Soft Delete Instance", "Soft Deleted": "Soft Deleted", "Soft Deleting": "Soft Deleting", "Soft Power Off": "Soft Power Off", "Soft Reboot": "Soft Reboot", "Soft Reboot Instance": "Soft Reboot Instance", "Soft Rebooting": "Soft Rebooting", "Soft-Affinity": "Soft-Affinity", "Soft-Anti-Affinity": "Soft-Anti-Affinity", "Solomon Islands": "Solomon Islands", "Somalia": "Somalia", "Sorry, the page you visited does not exist.": "Sorry, the page you visited does not exist.", "Source": "Source", "Source IP": "Source IP", "Source IP Address/Subnet": "Source IP Address/Subnet", "Source Path: {path}": "Source Path: {path}", "Source Port": "Source Port", "Source Port/Port Range": "Source Port/Port Range", "South Africa": "South Africa", "South Korea": "South Korea", "Spain": "Spain", "Spawning": "Spawning", "Spec": "Spec", "Specification": "Specification", "Specify Physical Node": "Specify Physical Node", "Specify mount point.": "Specify mount point.", "Specify the client IP address": "Specify the client IP address", "Specify the listener port": "Specify the listener port", "Specify whether future replicated instances will be created on the same hypervisor (affinity) or on different hypervisors (anti-affinity). This value is ignored if the instance to be launched is a replica.": "Specify whether future replicated instances will be created on the same hypervisor (affinity) or on different hypervisors (anti-affinity). This value is ignored if the instance to be launched is a replica.", "Specs": "Specs", "Sri Lanka": "Sri Lanka", "Stack": "<PERSON><PERSON>", "Stack Detail": "Stack Detail", "Stack Events": "Stack Events", "Stack Faults": "<PERSON><PERSON>s", "Stack ID": "Stack ID", "Stack Name": "Stack Name", "Stack Resource": "Stack Resource", "Stack Resource Type": "Stack Resource Type", "Stack Resources": "Stack Resources", "Stack Status": "Stack Status", "Stacks": "Stacks", "Stand Alone Ports Supported": "Stand Alone Ports Supported", "Standard Trait": "Standard Trait", "Start": "Start", "Start Container": "Start Container", "Start Instance": "Start Instance", "Start Of Authority": "Start Of Authority", "Start Source": "Start Source", "Start Source Name": "Start Source Name", "Start Time": "Start Time", "Start auto refreshing data": "Start auto refreshing data", "Start refreshing data every {num} seconds": "Start refreshing data every {num} seconds", "Started At": "Started At", "Startup Parameters": "Startup Parameters", "State": "State", "Static Routes": "Static Routes", "Stats Information": "Stats Information", "Status Code": "Status Code", "Status Detail": "Status Detail", "Status Reason": "Status Reason", "Stop": "Stop", "Stop Container": "Stop Container", "Stop Database Service": "Stop Database Service", "Stop Instance": "Stop Instance", "Stop auto refreshing data": "Stop auto refreshing data", "Stop refreshing data every {num} seconds": "Stop refreshing data every {num} seconds", "Stopped": "Stopped", "Storage": "Storage", "Storage Backends": "Storage Backends", "Storage Capacity(GiB)": "Storage Capacity(GiB)", "Storage Cluster Bandwidth": "Storage Cluster Bandwidth", "Storage Cluster IOPS": "Storage Cluster IOPS", "Storage Cluster OSD Latency": "Storage Cluster OSD Latency", "Storage Cluster Status": "Storage Cluster Status", "Storage Cluster Usage": "Storage Cluster Usage", "Storage Clusters": "Storage Clusters", "Storage IOPS": "Storage IOPS", "Storage Interface": "Storage Interface", "Storage Policy": "Storage Policy", "Storage Pool Capacity Usage": "Storage Pool Capacity Usage", "Storage Types": "Storage Types", "Sub Users": "Sub Users", "Subnet": "Subnet", "Subnet Count": "Subnet Count", "Subnet Detail": "Subnet Detail", "Subnet ID": "Subnet ID", "Subnet ID/Name": "Subnet ID/Name", "Subnet Name": "Subnet Name", "Subnets": "Subnets", "Subordinate Projects": "Subordinate Projects", "Subordinate User Groups": "Subordinate User Groups", "Succeeded": "Succeeded", "Success": "Success", "Sudan": "Sudan", "Supports resumable transfer (recommended when uploading a large image)": "Supports resumable transfer (recommended when uploading a large image)", "Suriname": "Suriname", "Suspend": "Suspend", "Suspend Complete": "Suspend Complete", "Suspend Failed": "Suspend Failed", "Suspend In Progress": "Suspend In Progress", "Suspend Instance": "Suspend Instance", "Suspended": "Suspended", "Suspending": "Suspending", "Swaziland": "Swaziland", "Sweden": "Sweden", "Switch ID": "Switch ID", "Switch Info": "Switch Info", "Switch Language": "Switch Language", "Switch Project": "Switch Project", "Switzerland": "Switzerland", "Syncing": "Syncing", "Syrian Arab Republic": "Syrian Arab Republic", "System": "System", "System Config": "System Config", "System Disk": "System Disk", "System Info": "System Info", "System Load": "System Load", "System Roles": "System Roles", "System Running Time": "System Running Time", "System is error, please try again later.": "System is error, please try again later.", "TCP": "TCP", "TCP Connections": "TCP Connections", "TLS Disabled": "TLS Disabled", "TTL": "TTL", "TTL (Time to Live) for the zone.": "TTL (Time to Live) for the zone.", "Tag is no longer than 60 characters": "Tag is no longer than 60 characters", "Tags": "Tags", "Tags Info": "Tags Info", "Tags are not case sensitive": "Tags are not case sensitive", "Taiwan": "Taiwan", "Tajikistan": "Tajikistan", "Take effect after restart": "Take effect after restart", "Tanzania": "Tanzania", "Target Compute Host": "Target Compute Host", "Target IP Address": "Target IP Address", "Target Port": "Target Port", "Target Project": "Target Project", "Target Project ID": "Target Project ID", "Target Project ID/Name": "Target Project ID/Name", "Target Project Name": "Target Project Name", "Target Storage Backend": "Target Storage Backend", "Target Tenant": "Target Tenant", "Task State": "Task State", "Template Content": "Template Content", "Template Name": "Template Name", "Text Record": "Text Record", "Thailand": "Thailand", "That is, after how many consecutive failures of the health check, the health check status of the back-end cloud server is changed from normal to abnormal": "That is, after how many consecutive failures of the health check, the health check status of the back-end cloud server is changed from normal to abnormal", "The DNS nameserver to use for this cluster template": "The DNS nameserver to use for this cluster template", "The Federation of Saint Kitts and Nevis": "The Federation of Saint Kitts and Nevis", "The Provider is the encryption provider format (e.g. \"luks\")": "The Provider is the encryption provider format (e.g. \"luks\")", "The Republic of Macedonia": "The Republic of Macedonia", "The Republic of South Sudan": "The Republic of South Sudan", "The SSH key is a way to remotely log in to the cluster instance. If it’s not set, the value of this in the template will be used.": "The SSH key is a way to remotely log in to the cluster instance. If it’s not set, the value of this in the template will be used.", "The SSH key is a way to remotely log in to the cluster instance. The cloud platform only helps to keep the public key. Please keep your private key properly.": "The SSH key is a way to remotely log in to the cluster instance. The cloud platform only helps to keep the public key. Please keep your private key properly.", "The SSH key is a way to remotely log in to the instance. The cloud platform only helps to keep the public key. Please keep your private key properly.": "The SSH key is a way to remotely log in to the instance. The cloud platform only helps to keep the public key. Please keep your private key properly.", "The amphora instance is required for load balancing service setup and is not recommended": "The amphora instance is required for load balancing service setup and is not recommended", "The associated floating IP, virtual adapter, volume and other resources will be automatically disassociated.": "The associated floating IP, virtual adapter, volume and other resources will be automatically disassociated.", "The certificate contains information such as the public key and signature of the certificate. The extension of the certificate is \"pem\" or \"crt\", you can directly enter certificate content or upload certificate file.": "The certificate contains information such as the public key and signature of the certificate. The extension of the certificate is \"pem\" or \"crt\", you can directly enter certificate content or upload certificate file.", "The changed node count can not be equal to the current value": "The changed node count can not be equal to the current value", "The command to execute": "The command to execute", "The container memory size in MiB": "The container memory size in MiB", "The container runtime tool to create container with": "The container runtime tool to create container with", "The creation instruction has been issued, please refresh to see the actual situation in the list.": "The creation instruction has been issued, please refresh to see the actual situation in the list.", "The creation instruction was issued successfully, instance: {name}. \n You can wait for a few seconds to follow the changes of the list data or manually refresh the data to get the final display result.": "The creation instruction was issued successfully, instance: {name}. \n You can wait for a few seconds to follow the changes of the list data or manually refresh the data to get the final display result.", "The current operation requires the instance to be shut down:": "The current operation requires the instance to be shut down:", "The current platform has not yet enabled the {name} management module. Please contact the administrator to enable it": "The current platform has not yet enabled the {name} management module. Please contact the administrator to enable it", "The description can be up to 255 characters long.": "The description can be up to 255 characters long.", "The disk size in GiB for per container": "The disk size in GiB for per container", "The domain name can only be composed of letters, numbers, dashes, in A dash cannot be at the beginning or end, and a single string cannot exceed more than 63 characters, separated by dots; At most can support 30 domain names, separated by commas;The length of a single domain name does not exceed 100 characters, and the total length degree does not exceed 1024 characters.": "The domain name can only be composed of letters, numbers, dashes, in A dash cannot be at the beginning or end, and a single string cannot exceed more than 63 characters, separated by dots; At most can support 30 domain names, separated by commas;The length of a single domain name does not exceed 100 characters, and the total length degree does not exceed 1024 characters.", "The entire inspection process takes 5 to 10 minutes, so you need to be patient. After the registration is completed, the node configuration status will return to the manageable status.": "The entire inspection process takes 5 to 10 minutes, so you need to be patient. After the registration is completed, the node configuration status will return to the manageable status.", "The entrypoint which overwrites the default ENTRYPOINT of the image": "The entrypoint which overwrites the default ENTRYPOINT of the image", "The feasible configuration of cloud-init or cloudbase-init service in the image is not synced to image's properties, so the Login Name is unknown.": "The feasible configuration of cloud-init or cloudbase-init service in the image is not synced to image's properties, so the Login Name is unknown.", "The file with the same name will be overwritten.": "The file with the same name will be overwritten.", "The floating IP configured with port forwardings cannot be bound": "The floating IP configured with port forwardings cannot be bound", "The format of the certificate content is: by \"----BEGIN CERTIFICATE-----\" as the beginning,\"-----END CERTIFICATE----\" as the end, 64 characters per line, the last line does not exceed 64 characters, and there cannot be blank lines.": "The format of the certificate content is: by \"----BEGIN CERTIFICATE-----\" as the beginning,\"-----END CERTIFICATE----\" as the end, 64 characters per line, the last line does not exceed 64 characters, and there cannot be blank lines.", "The host name of this container": "The host name of this container", "The http_proxy address to use for nodes in cluster": "The http_proxy address to use for nodes in cluster", "The https_proxy address to use for nodes in cluster": "The https_proxy address to use for nodes in cluster", "The image is not existed": "The image is not existed", "The instance architecture diagram mainly shows the overall architecture composition of the instance. If you need to view the network topology of the instance, please go to: ": "The instance architecture diagram mainly shows the overall architecture composition of the instance. If you need to view the network topology of the instance, please go to: ", "The instance deleted immediately cannot be restored": "The instance deleted immediately cannot be restored", "The instance has been locked. If you want to do more, please unlock it first.": "The instance has been locked. If you want to do more, please unlock it first.", "The instance is not shut down, unable to restore.": "The instance is not shut down, unable to restore.", "The instance which is boot from volume will create snapshots for each mounted volumes.": "The instance which is boot from volume will create snapshots for each mounted volumes.", "The instances in the affinity group are allocated to the same physical machine as much as possible, and when there are no more physical machines to allocate, the normal allocation strategy is returned.": "The instances in the affinity group are allocated to the same physical machine as much as possible, and when there are no more physical machines to allocate, the normal allocation strategy is returned.", "The instances in the affinity group are strictly allocated to the same physical machine. When there are no more physical machines to allocate, the allocation fails.": "The instances in the affinity group are strictly allocated to the same physical machine. When there are no more physical machines to allocate, the allocation fails.", "The instances in the anti-affinity group are allocated to different physical machines as much as possible. When there are no more physical machines to allocate, the normal allocation strategy is returned.": "The instances in the anti-affinity group are allocated to different physical machines as much as possible. When there are no more physical machines to allocate, the normal allocation strategy is returned.", "The instances in the anti-affinity group are strictly allocated to different physical machines. When there are no more physical machines to allocate, the allocation fails.": "The instances in the anti-affinity group are strictly allocated to different physical machines. When there are no more physical machines to allocate, the allocation fails.", "The ip address {ip} is duplicated, please modify it.": "The ip address {ip} is duplicated, please modify it.", "The ip is not within the allocated pool!": "The ip is not within the allocated pool!", "The ip of external members can be any, including the public network ip.": "The ip of external members can be any, including the public network ip.", "The key pair allows you to SSH into your newly created instance. You can select an existing key pair, import a key pair, or generate a new key pair.": "The key pair allows you to SSH into your newly created instance. You can select an existing key pair, import a key pair, or generate a new key pair.", "The kill signal to send": "The kill signal to send", "The limit of cluster instance greater than or equal to 1.": "The limit of cluster instance greater than or equal to 1.", "The maximum batch size is {size}, that is, the size of the port range cannot exceed {size}.": "The maximum batch size is {size}, that is, the size of the port range cannot exceed {size}.", "The maximum transmission unit (MTU) value to address fragmentation. Minimum value is 68 for IPv4, and 1280 for IPv6.": "The maximum transmission unit (MTU) value to address fragmentation. Minimum value is 68 for IPv4, and 1280 for IPv6.", "The min size is {size} GiB": "The min size is {size} GiB", "The name of the physical network to which a port is connected": "The name of the physical network to which a port is connected", "The name should be end with \".\"": "The name should be end with \".\"", "The name should contain letter or number, the length is 1 to 16, characters can only contain \"0-9, a-z, A-Z, -, _.\"": "The name should contain letter or number, the length is 1 to 16, characters can only contain \"0-9, a-z, A-Z, -, _.\"", "The name should contain letter or number, the length is 2 to 64, characters can only contain \"0-9, a-z, A-Z, -, _.\"": "The name should contain letter or number, the length is 2 to 64, characters can only contain \"0-9, a-z, A-Z, -, _.\"", "The name should start with letter or number, and be a string of 2 to 255, characters can only contain \"0-9, a-z, A-Z, -, _, .\"": "The name should start with letter or number, and be a string of 2 to 255, characters can only contain \"0-9, a-z, A-Z, -, _, .\"", "The name should start with upper letter or lower letter, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, \"-'_()[].:^\".": "The name should start with upper letter or lower letter, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, \"-'_()[].:^\".", "The name should start with upper letter or lower letter, characters can only contain \"0-9, a-z, A-Z, -, _, .\"": "The name should start with upper letter or lower letter, characters can only contain \"0-9, a-z, A-Z, -, _, .\"", "The name should start with upper letter, lower letter or chinese, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, \"-'_()[].\".": "The name should start with upper letter, lower letter or chinese, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, \"-'_()[].\".", "The name should start with upper letter, lower letter or chinese, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, \"-'_()[].:^\".": "The name should start with upper letter, lower letter or chinese, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, \"-'_()[].:^\".", "The name should start with upper letter, lower letter or chinese, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, \"-'_.\".": "The name should start with upper letter, lower letter or chinese, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, \"-'_.\".", "The name should start with upper letter, lower letter or chinese, and be a string of 1 to 64, characters can only contain \"0-9, a-z, A-Z, \"-'_()[].^\".": "The name should start with upper letter, lower letter or chinese, and be a string of 1 to 64, characters can only contain \"0-9, a-z, A-Z, \"-'_()[].^\".", "The name should start with upper letter, lower letter or chinese, and be a string of 3 to 63, characters can only contain \"0-9, a-z, A-Z, chinese, -, .\".": "The name should start with upper letter, lower letter or chinese, and be a string of 3 to 63, characters can only contain \"0-9, a-z, A-Z, chinese, -, .\".", "The name should start with upper letter, lower letter, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, -, _\".": "The name should start with upper letter, lower letter, and be a string of 1 to 128, characters can only contain \"0-9, a-z, A-Z, -, _\".", "The name should start with upper letter, lower letter, and be a string of 2 to 255, characters can only contain \"0-9, a-z, A-Z, -, ., _\".": "The name should start with upper letter, lower letter, and be a string of 2 to 255, characters can only contain \"0-9, a-z, A-Z, -, ., _\".", "The name should start with upper letter, lower letter, and be a string of 3 to 63, characters can only contain \"0-9, a-z, A-Z, -\".": "The name should start with upper letter, lower letter, and be a string of 3 to 63, characters can only contain \"0-9, a-z, A-Z, -\".", "The new password cannot be identical to the current password.": "The new password cannot be identical to the current password.", "The no_proxy address to use for nodes in cluster": "The no_proxy address to use for nodes in cluster", "The number of allowed key pairs for each user.": "The number of allowed key pairs for each user.", "The number of vCPU cores should not exceed the maximum number of CPU cores of the physical node. Otherwise it will cause fail to schedule to any physical node when creating instance.": "The number of vCPU cores should not exceed the maximum number of CPU cores of the physical node. Otherwise it will cause fail to schedule to any physical node when creating instance.", "The number of virtual cpu for this container": "The number of virtual cpu for this container", "The optional headers to insert into the request before it is sent to the backend member.": "The optional headers to insert into the request before it is sent to the backend member.", "The password must not be the same as the previous": "The password must not be the same as the previous", "The password must not be the same as the previous two": "The password must not be the same as the previous two", "The password must not be the same as the previous {num}": "The password must not be the same as the previous {num}", "The port created here will be automatically deleted when detach. If you need a reusable port, please go to the Virtual Adapter page to create and attach the port to instance.": "The port created here will be automatically deleted when detach. If you need a reusable port, please go to the Virtual Adapter page to create and attach the port to instance.", "The private key content format is: with \"-----BEGIN RSA PRIVATE KEY-----\" as the beginning,\"-----END RSA PRIVATE KEY-----\" as the end, 64 characters per line, the last line does not exceed 64 characters, and there cannot be blank lines.": "The private key content format is: with \"-----BEGIN RSA PRIVATE KEY-----\" as the beginning,\"-----END RSA PRIVATE KEY-----\" as the end, 64 characters per line, the last line does not exceed 64 characters, and there cannot be blank lines.", "The private key of the certificate, the extension of the private key is \"key\", you can directly enter the content of the private key file or upload a private key that conforms to the format document.": "The private key of the certificate, the extension of the private key is \"key\", you can directly enter the content of the private key file or upload a private key that conforms to the format document.", "The resource class of the scheduled node needs to correspond to the resource class name of the flavor used by the ironic instance (for example, the resource class name of the scheduling node is baremetal.with-GPU, and the custom resource class name of the flavor is CUSTOM_BAREMETAL_WITH_GPU=1).": "The resource class of the scheduled node needs to correspond to the resource class name of the flavor used by the ironic instance (for example, the resource class name of the scheduling node is baremetal.with-GPU, and the custom resource class name of the flavor is CUSTOM_BAREMETAL_WITH_GPU=1).", "The resource has been deleted": "The resource has been deleted", "The root and os_admin are default users and cannot be created!": "The root and os_admin are default users and cannot be created!", "The root disk of the instance has snapshots": "The root disk of the instance has snapshots", "The security group is similar to the firewall function and is used to set up network access control. ": "The security group is similar to the firewall function and is used to set up network access control. ", "The security group is similar to the firewall function for setting up network access control, or you can go to the console and create a new security group. (Note: The security group you selected will work on all virtual LANs on the instances.)": "The security group is similar to the firewall function for setting up network access control, or you can go to the console and create a new security group. (Note: The security group you selected will work on all virtual LANs on the instances.)", "The selected VPC/subnet does not have IPv6 enabled.": "The selected VPC/subnet does not have IPv6 enabled.", "The selected network has no subnet": "The selected network has no subnet", "The selected project is different from the project to which the network belongs. That is, the subnet to be created is not under the same project as the network. Please do not continue unless you are quite sure what you are doing.": "The selected project is different from the project to which the network belongs. That is, the subnet to be created is not under the same project as the network. Please do not continue unless you are quite sure what you are doing.", "The session has expired, please log in again.": "The session has expired, please log in again.", "The shelved offloaded instance only supports immediate deletion": "The shelved offloaded instance only supports immediate deletion", "The size of the external port range is required to be the same as the size of the internal port range": "The size of the external port range is required to be the same as the size of the internal port range", "The start source is a template used to create an instance. You can choose an image or a bootable volume.": "The start source is a template used to create an instance. You can choose an image or a bootable volume.", "The starting number must be less than the ending number": "The starting number must be less than the ending number", "The timeout for cluster creation in minutes.": "The timeout for cluster creation in minutes.", "The timeout period of waiting for the return of the health check request, the check timeout will be judged as a check failure": "The timeout period of waiting for the return of the health check request, the check timeout will be judged as a check failure", "The total amount of data is { total }, and the interface can support downloading { totalMax } pieces of data. If you need to download all the data, please contact the administrator.": "The total amount of data is { total }, and the interface can support downloading { totalMax } pieces of data. If you need to download all the data, please contact the administrator.", "The trait name of the flavor needs to correspond to the trait of the scheduling node; by injecting the necessary traits into the ironic instance, the computing service will only schedule the instance to the bare metal node with all necessary traits (for example: the trait of the scheduling node has HW_CPU_X86_VMX trait, and the flavor adds HW_CPU_X86_VMX, it can be scheduled to this node for necessary traits).": "The trait name of the flavor needs to correspond to the trait of the scheduling node; by injecting the necessary traits into the ironic instance, the computing service will only schedule the instance to the bare metal node with all necessary traits (for example: the trait of the scheduling node has HW_CPU_X86_VMX trait, and the flavor adds HW_CPU_X86_VMX, it can be scheduled to this node for necessary traits).", "The trait of the scheduled node needs to correspond to the trait of the flavor used by the ironic instance; by injecting the necessary traits into the ironic instance, the computing service will only schedule the instance to the bare metal node with all the necessary traits (for example, the ironic instance which use the flavor that has HW_CPU_X86_VMX as a necessary trait, can be scheduled to the node which has the trait of HW_CPU_X86_VMX).": "The trait of the scheduled node needs to correspond to the trait of the flavor used by the ironic instance; by injecting the necessary traits into the ironic instance, the computing service will only schedule the instance to the bare metal node with all the necessary traits (for example, the ironic instance which use the flavor that has HW_CPU_X86_VMX as a necessary trait, can be scheduled to the node which has the trait of HW_CPU_X86_VMX).", "The unit suffix must be one of the following: Kb(it), Kib(it), Mb(it), Mib(it), Gb(it), Gib(it), Tb(it), Tib(it), KB, KiB, MB, MiB, GB, GiB, TB, TiB. If the unit suffix is not provided, it is assumed to be KB.": "The unit suffix must be one of the following: Kb(it), <PERSON>b(it), <PERSON>b(it), <PERSON>b(it), Gb(it), <PERSON>ib(it), Tb(it), Tib(it), KB, KiB, MB, MiB, GB, GiB, TB, TiB. If the unit suffix is not provided, it is assumed to be KB.", "The user has been disabled, please contact the administrator": "The user has been disabled, please contact the administrator", "The user needs to ensure that the input is a shell script that can run completely and normally.": "The user needs to ensure that the input is a shell script that can run completely and normally.", "The value of the upper limit of the range must be greater than the value of the lower limit of the range.": "The value of the upper limit of the range must be greater than the value of the lower limit of the range.", "The volume associated with the backup is not available, unable to restore.": "The volume associated with the backup is not available, unable to restore.", "The volume status can be reset to in-use only when the previous status is in-use.": "The volume status can be reset to in-use only when the previous status is in-use.", "The volume type needs to be consistent with the volume type when the snapshot is created.": "The volume type needs to be consistent with the volume type when the snapshot is created.", "The volume type needs to set \"multiattach\" in the metadata to support shared volume attributes.": "The volume type needs to set \"multiattach\" in the metadata to support shared volume attributes.", "The working directory for commands to run in": "The working directory for commands to run in", "The zone name should end with \".\"": "The zone name should end with \".\"", "The {action} instruction has been issued, instance: {name}. \n You can wait for a few seconds to follow the changes of the list data or manually refresh the data to get the final display result.": "The {action} instruction has been issued, instance: {name}. \n You can wait for a few seconds to follow the changes of the list data or manually refresh the data to get the final display result.", "The {action} instruction has been issued. \n You can wait for a few seconds to follow the changes of the list data or manually refresh the data to get the final display result.": "The {action} instruction has been issued. \n You can wait for a few seconds to follow the changes of the list data or manually refresh the data to get the final display result.", "The {name} has already been used by other {resource}({content}), please change.": "The {name} has already been used by other {resource}({content}), please change.", "The {name} {ports} have already been used, please change.": "The {name} {ports} have already been used, please change.", "There are resources that cannot {action} in the selected resources, such as:": "There are resources that cannot {action} in the selected resources, such as:", "There are resources that cannot {action} in the selected resources.": "There are resources that cannot {action} in the selected resources.", "There are resources under the project and cannot be deleted.": "There are resources under the project and cannot be deleted.", "There is currently a image that needs to continue uploading. Please refresh page and upload the image. The new image cannot be used for the time being.": "There is currently a image that needs to continue uploading. Please refresh page and upload the image. The new image cannot be used for the time being.", "There is currently no file to paste.": "There is currently no file to paste.", "This operation creates a security group with default security group rules for the IPv4 and IPv6 ether types.": "This operation creates a security group with default security group rules for the IPv4 and IPv6 ether types.", "This service will automatically query the configuration (CPU, memory, etc.) and MAC address of the physical machine, and the ironic-inspector service will automatically register this information in the node information.": "This service will automatically query the configuration (CPU, memory, etc.) and MAC address of the physical machine, and the ironic-inspector service will automatically register this information in the node information.", "This will delete all child objects of the load balancer.": "This will delete all child objects of the load balancer.", "Threads Activity Trends": "Threads Activity Trends", "Time Interval: ": "Time Interval: ", "Time To Live": "Time To Live", "Time To Live in seconds.": "Time To Live in seconds.", "Time between running the check in seconds": "Time between running the check in seconds", "Timeout(Minute)": "Timeout(Minute)", "Timeout(s)": "Timeout(s)", "Tips: without domain means \"Default\" domain.": "Tips: without domain means \"Default\" domain.", "To open": "To open", "Today CPU usage > 80% alert": "Today CPU usage > 80% alert", "Today Memory usage > 80% alert": "Today Memory usage > 80% alert", "Togo": "Togo", "Tokelau": "Tokelau", "Tonga": "Tonga", "Too many disks mounted on the instance will affect the read and write performance. It is recommended not to exceed 16 disks.": "Too many disks mounted on the instance will affect the read and write performance. It is recommended not to exceed 16 disks.", "Topic": "Topic", "Topology": "Topology", "Total": "Total", "Total Capacity": "Total Capacity", "Total Connections": "Total Connections", "Total Consumers": "Total Consumers", "Total Containers": "Total Containers", "Total Exchanges": "Total Exchanges", "Total IPs": "Total IPs", "Total Queues": "Total Queues", "Total Ram": "Total Ram", "Total {total} items": "Total {total} items", "Trait Properties": "Trait Properties", "Traits": "Trai<PERSON>", "Transfer ID": "Transfer ID", "Transfer Name": "Transfer Name", "Transferred": "Transferred", "Transform Protocol": "Transform Protocol", "Trinidad and Tobago": "Trinidad and Tobago", "True": "True", "Tunisia": "Tunisia", "Turkey": "Turkey", "Turkmenistan": "Turkmenistan", "Turks and Caicos Islands": "Turks and Caicos Islands", "Tuvalu": "Tuvalu", "Two-way authentication": "Two-way authentication", "Type": "Type", "UDP": "UDP", "UDPLite": "UDPLite", "UNHEALTHY": "UNHEALTHY", "UNKNOWN": "UNKNOWN", "UOS": "UOS", "UPDATE COMPLETE": "UPDATE COMPLETE", "UPDATE FAILED": "UPDATE FAILED", "UPDATE IN PROGRESS": "UPDATE IN PROGRESS", "USB Info": "USB Info", "USB Parameters": "USB Parameters", "USB model, used when configuring instance flavor": "USB model, used when configuring instance flavor", "USER": "USER", "UStack": "UStack", "UStack Reports": "UStack Reports", "UStack Services": "UStack Services", "UUID": "UUID", "Ubuntu": "Ubuntu", "Uccps": "Uccps", "Uganda": "Uganda", "Ukraine": "Ukraine", "Unable to create instance: batch creation is not supported when specifying IP.": "Unable to create instance: batch creation is not supported when specifying IP.", "Unable to create instance: insufficient quota to create resources.": "Unable to create instance: insufficient quota to create resources.", "Unable to create volume: insufficient quota to create resources.": "Unable to create volume: insufficient quota to create resources.", "Unable to delete router \"{ name }\". External gateway is opened, please clear external gateway first.": "Unable to delete router \"{ name }\". External gateway is opened, please clear external gateway first.", "Unable to get {name} detail.": "Unable to get {name} detail.", "Unable to get {name}.": "Unable to get {name}.", "Unable to get {title}, please go back to ": "Unable to get {title}, please go back to ", "Unable to get {title}, please go to ": "Unable to get {title}, please go to ", "Unable to paste into the same folder.": "Unable to paste into the same folder.", "Unable to render form": "Unable to render form", "Unable to {action} {name}.": "Unable to {action} {name}.", "Unable to {action}, because : {reason}, instance: {name}.": "Unable to {action}, because : {reason}, instance: {name}.", "Unable to {action}, instance: {name}.": "Unable to {action}, instance: {name}.", "Unable to {action}.": "Unable to {action}.", "Unable to {title}, please go back to ": "Unable to {title}, please go back to ", "Unattached": "Unattached", "Unavailable": "Unavailable", "Unbootable": "Unbootable", "Unbounded": "Unbounded", "United Arab Emirates": "United Arab Emirates", "United Kingdom": "United Kingdom", "United States": "United States", "Unknown": "Unknown", "Unless Stopped": "Unless Stopped", "Unless you know clearly which AZ to create the volume in, you don not need to fill in here.": "Unless you know clearly which AZ to create the volume in, you don not need to fill in here.", "Unlimit": "Unlimit", "Unlock": "Unlock", "Unlock Instance": "Unlock Instance", "Unmanage Error": "Unmanage Error", "Unmanage Starting": "Unmanage Starting", "Unmanaged": "Unmanaged", "Unpause": "Unpause", "Unpause Container": "Unpause Container", "Unpause Instance": "Unpause Instance", "Unrescuing": "Unrescuing", "Unrestricted": "Unrestricted", "Unset": "Unset", "Unshelve": "Unshelve", "Unshelve Instance": "Unshelve Instance", "Unshelving": "Unshelving", "Unused": "Unused", "Up": "Up", "Update": "Update", "Update Access": "Update Access", "Update At": "Update At", "Update Cluster Template": "Update Cluster Template", "Update Complete": "Update Complete", "Update Failed": "Update Failed", "Update In Progress": "Update In Progress", "Update Record Set": "Update Record Set", "Update Segment": "Update Segment", "Update Status": "Update Status", "Update Template": "Update Template", "Update User Password": "Update User Password", "Update user password": "Update user password", "Updated": "Updated", "Updated At": "Updated At", "Updating": "Updating", "Updating Password": "Updating Password", "Upgrade Cluster": "Upgrade Cluster", "Upload File": "Upload File", "Upload Type": "Upload Type", "Upload progress": "Upload progress", "Uploading": "Uploading", "Uploads with mirrors are interrupted. The name of the mirror file is: {name}. Do you want to continue the image? Please select the mirror file in the button below and click the \"Continue\" button; Tips: If the wrong file is selected, it will not be interrupted. Clicking the \"Delete\" button will delete the task of uploading the image.": "Uploads with mirrors are interrupted. The name of the mirror file is: {name}. Do you want to continue the image? Please select the mirror file in the button below and click the \"Continue\" button; Tips: If the wrong file is selected, it will not be interrupted. Clicking the \"Delete\" button will delete the task of uploading the image.", "Uruguay": "Uruguay", "Usage": "Usage", "Usage Type": "Usage Type", "Usb Controller": "Usb Controller", "Use Type": "Use Type", "Used": "Used", "Used IPs": "Used IPs", "Used by tunnel(s): {names}. ID(s): {ids}": "Used by tunnel(s): {names}. ID(s): {ids}", "Used to restrict whether the application credential may be used for the creation or destruction of other application credentials or trusts.": "Used to restrict whether the application credential may be used for the creation or destruction of other application credentials or trusts.", "User": "User", "User Account": "User Account", "User Center": "User Center", "User Data": "User Data", "User Detail": "User Detail", "User Edit": "User Edit", "User Group": "User Group", "User Group Detail": "User Group Detail", "User Group ID/Name": "User Group ID/Name", "User Group Name": "User Group Name", "User Group Num": "User Group Num", "User Group Num: ": "User Group Num: ", "User Groups": "User Groups", "User ID": "User ID", "User ID/Name": "User ID/Name", "User Name": "User Name", "User Num": "User <PERSON>um", "User Num: ": "User Num: ", "User name can not be duplicated": "User name can not be duplicated", "User need to change password": "User need to change password", "Username": "Username", "Captcha incorrect": "<PERSON><PERSON> <PERSON>", "Captcha": "<PERSON><PERSON>", "Login failures exceeded": "Login failures exceeded, please try again later", "Too many failed login attempts. Please try again later after 1 minute.": "Too many failed login attempts. Please try again later after 1 minute.", "Username or password is incorrect": "Username or password is incorrect", "Users": "Users", "Using cascading deletion, when the volume has snapshots, the associated snapshot will be automatically deleted first, and then the volume will be deleted, thereby improving the success rate of deleting the volume.": "Using cascading deletion, when the volume has snapshots, the associated snapshot will be automatically deleted first, and then the volume will be deleted, thereby improving the success rate of deleting the volume.", "Using server groups, you can create cloud hosts on the same/different physical nodes as much as possible to meet the affinity/non-affinity requirements of business applications.": "Using server groups, you can create cloud hosts on the same/different physical nodes as much as possible to meet the affinity/non-affinity requirements of business applications.", "Uzbekistan": "Uzbekistan", "VCPU (Core)": "VCPU (Core)", "VCPUs": "VCPUs", "VDI - VirtualBox compatible image format": "VDI - VirtualBox compatible image format", "VGPU": "VGPU", "VGPU (Core)": "VGPU (Core)", "VHD - VirtualPC compatible image format": "VHD - VirtualPC compatible image format", "VIF Details": "VIF Details", "VIF Type": "VIF Type", "VIR Domain Event": "VIR Domain Event", "VMDK - Hyper-V compatible image format": "VMDK - Hyper-V compatible image format", "VNC": "VNC", "VNIC Type": "VNIC Type", "VPN": "VPN", "VPN EndPoint Groups": "VPN EndPoint Groups", "VPN Gateways": "VPN Gateways", "VPN Service": "VPN Service", "VPN Service ID": "VPN Service ID", "VPNs": "VPNs", "VRRP": "VRRP", "Valid": "<PERSON><PERSON>", "Values": "Values", "Vanuatu": "Vanuatu", "Vatican City State (Holy See)": "Vatican City State (Holy See)", "Vendor Interface": "Vendor Interface", "Venezuela": "Venezuela", "Verifying": "Verifying", "Version": "Version", "Vietnam": "Vietnam", "View": "View", "View Detail": "View Detail", "View Full Log": "View Full Log", "View Rules": "View Rules", "View virtual adapters": "View virtual adapters", "Virgin Islands (U.S.)": "Virgin Islands (U.S.)", "Virtual Adapter": "Virtual Adapter", "Virtual Adapter ID": "Virtual Adapter ID", "Virtual LAN": "Virtual LAN", "Virtual LANs": "Virtual LANs", "Virtual Resource Overview": "Virtual Resource Overview", "Virtual Resources Used": "Virtual Resources Used", "Virtual adapter mainly used for binding instance and other operations, occupying the quota of the port.": "Virtual adapter mainly used for binding instance and other operations, occupying the quota of the port.", "Visibility": "Visibility", "Visualization Compute Optimized Type with GPU": "Visualization Compute Optimized Type with GPU", "Volume": "Volume", "Volume Backup": "Volume Backup", "Volume Backup Capacity (GiB)": "Volume Backup Capacity (GiB)", "Volume Backup Detail": "Volume Backup Detail", "Volume Backup Name": "Volume Backup Name", "Volume Backups": "Volume Backups", "Volume Capacity (GiB)": "Volume Capacity (GiB)", "Volume Detail": "Volume Detail", "Volume Driver": "Volume Driver", "Volume ID": "Volume ID", "Volume ID/Name": "Volume ID/Name", "Volume Info": "Volume Info", "Volume Name": "Volume Name", "Volume Size": "Volume Size", "Volume Snapshot": "Volume Snapshot", "Volume Snapshot Detail": "Volume Snapshot Detail", "Volume Snapshot Name": "Volume Snapshot Name", "Volume Snapshots": "Volume Snapshots", "Volume Source": "Volume Source", "Volume Transfer": "Volume Transfer", "Volume Type": "Volume Type", "Volume Type Detail": "Volume Type Detail", "Volume Types": "Volume Types", "Volumes": "Volumes", "Wallis And Futuna Islands": "Wallis And Futuna Islands", "Warn": "<PERSON><PERSON>", "Weight": "Weight", "Weights": "Weights", "Welcome": "Welcome", "Western Sahara": "Western Sahara", "When auto-expand/close is enabled, if there is no operation in the pop-up window, the pop-up window will be closed automatically after { seconds } seconds, and it will be automatically expanded when the displayed content changes.": "When auto-expand/close is enabled, if there is no operation in the pop-up window, the pop-up window will be closed automatically after { seconds } seconds, and it will be automatically expanded when the displayed content changes.", "When the computing service starts the recycling instance interval, the instance will be stored in the recycling bin after deletion, and will be retained according to the corresponding time interval. You can choose to restore it within this period. After successful recovery, the status of the instance is running and related resources remain unchanged.": "When the computing service starts the recycling instance interval, the instance will be stored in the recycling bin after deletion, and will be retained according to the corresponding time interval. You can choose to restore it within this period. After successful recovery, the status of the instance is running and related resources remain unchanged.", "When the volume is \"bootable\" and the status is \"available\", it can be used as a startup source to create an instance.": "When the volume is \"bootable\" and the status is \"available\", it can be used as a startup source to create an instance.", "When you do online backup of the volume that has been bound, you need to pay attention to the following points:": "When you do online backup of the volume that has been bound, you need to pay attention to the following points:", "When you restore a backup, you need to meet one of the following conditions:": "When you restore a backup, you need to meet one of the following conditions:", "When your Yaml file is a fixed template, variable variables can be stored in an environment variable file to implement template deployment. The parameters in the environment variable file need to match the parameters defined in the template file.": "When your Yaml file is a fixed template, variable variables can be stored in an environment variable file to implement template deployment. The parameters in the environment variable file need to match the parameters defined in the template file.", "Whether enable or not using the floating IP of cloud provider.": "Whether enable or not using the floating IP of cloud provider.", "Whether the Login Name can be used is up to the feasible configuration of cloud-init or cloudbase-init service in the image.": "Whether the Login Name can be used is up to the feasible configuration of cloud-init or cloudbase-init service in the image.", "Whether the boot device should be set only for the next reboot, or persistently.": "Whether the boot device should be set only for the next reboot, or persistently.", "Which Network Interface provider to use when plumbing the network connections for this Node": "Which Network Interface provider to use when plumbing the network connections for this Node", "Windows": "Windows", "Workdir": "Workdir", "Working Directory": "Working Directory", "X86 Architecture": "X86 Architecture", "YAML File": "YAML File", "Yemen": "Yemen", "Yes": "Yes", "Yes - Create a new system disk": "Yes - Create a new system disk", "You are not allowed to delete policy \"{ name }\" used by firewalls: { firewalls }.": "You are not allowed to delete policy \"{ name }\" used by firewalls: { firewalls }.", "You are not allowed to delete policy \"{ name }\".": "You are not allowed to delete policy \"{ name }\".", "You are not allowed to delete router \"{ name }\".": "You are not allowed to delete router \"{ name }\".", "You are not allowed to delete rule \"{ name }\" in use.": "You are not allowed to delete rule \"{ name }\" in use.", "You are not allowed to delete rule \"{ name }\".": "You are not allowed to delete rule \"{ name }\".", "You are not allowed to delete snapshot \"{ name }\", which is used by creating volume \"{volumes}\".": "You are not allowed to delete snapshot \"{ name }\", which is used by creating volume \"{volumes}\".", "You are not allowed to delete snapshot \"{ name }\".": "You are not allowed to delete snapshot \"{ name }\".", "You are not allowed to jump to the console.": "You are not allowed to jump to the console.", "You are not allowed to { action } \"{ name }\".": "You are not allowed to { action } \"{ name }\".", "You are not allowed to { action } {name}.": "You are not allowed to { action } {name}.", "You are not allowed to {action}, instance: {name}.": "You are not allowed to {action}, instance: {name}.", "You are not allowed to {action}.": "You are not allowed to {action}.", "You can manually specify a physical node to create an instance.": "You can manually specify a physical node to create an instance.", "You don't have access to get {name}.": "You don't have access to get {name}.", "You may update the editable properties of the RBAC policy here.": "You may update the editable properties of the RBAC policy here.", "Yugoslavia": "Yugoslavia", "Zambia": "Zambia", "Zimbabwe": "Zimbabwe", "Zone": "Zone", "Zone ID": "Zone ID", "Zone ID/Name": "Zone ID/Name", "Zone Name": "Zone Name", "Zones Detail": "Zones Detail", "abandon stack": "abandon stack", "add access rule": "add access rule", "add network": "add network", "add router": "add router", "all": "all", "an optional string field to be used to store any vendor-specific information": "an optional string field to be used to store any vendor-specific information", "application credential": "application credential", "associate floating ip": "associate floating ip", "attach interface": "attach interface", "authorized by group ": "authorized by group ", "auto": "auto", "auto_priority": "auto_priority", "availability zones": "availability zones", "available": "available", "bare metal node": "bare metal node", "bare metal nodes": "bare metal nodes", "be copied": "be copied", "be cut": "be cut", "be deleted": "be deleted", "be rebooted": "be rebooted", "be recovered": "be recovered", "be released": "be released", "be soft rebooted": "be soft rebooted", "be started": "be started", "be stopped": "be stopped", "capsules": "capsules", "certificate": "certificate", "cidr": "CIDR", "cinder services": "cinder services", "clusters": "clusters", "clustertemplates": "clustertemplates", "compute hosts": "compute hosts", "compute services": "compute services", "configurations": "configurations", "confirm resize or migrate": "confirm resize or migrate", "connect subnet": "connect subnet", "container objects": "container objects", "containers": "containers", "create DSCP marking rule": "create DSCP marking rule", "create a new network/subnet": "create a new network/subnet", "create a new security group": "create a new security group", "create allowed address pair": "create allowed address pair", "create bandwidth limit rule": "create bandwidth limit rule", "create baremetal node": "create baremetal node", "create default pool": "create default pool", "create encryption": "create encryption", "create firewall policy": "create firewall policy", "create flavor": "create flavor", "create instance snapshot": "create instance snapshot", "create ipsec site connection": "create ipsec site connection", "create network": "create network", "create router": "create router", "create share": "create share", "create share group": "create share group", "create share group type": "create share group type", "create share network": "create share network", "create share type": "create share type", "create stack": "create stack", "create volume": "create volume", "create volume snapshot": "create volume snapshot", "create volume type": "create volume type", "create vpn": "create vpn", "create vpn endpoint group": "create vpn endpoint group", "create vpn ike policy": "create vpn ike policy", "create vpn ipsec policy": "create vpn ipsec policy", "data": "data", "database backups": "database backups", "database instances": "database instances", "delete": "delete", "delete allowed address pair": "delete allowed address pair", "delete application credential": "delete application credential", "delete bandwidth egress rules": "delete bandwidth egress rules", "delete bandwidth ingress rules": "delete bandwidth ingress rules", "delete certificate": "delete certificate", "delete container": "delete container", "delete default pool": "delete default pool", "delete domain": "delete domain", "delete dscp marking rules": "delete dscp marking rules", "delete firewall": "delete firewall", "delete flavor": "delete flavor", "delete group": "delete group", "delete host": "delete host", "delete image": "delete image", "delete instance": "delete instance", "delete instance snapshot": "delete instance snapshot", "delete ipsec site connection": "delete ipsec site connection", "delete ironic instance": "delete ironic instance", "delete keypair": "delete keypair", "delete listener": "delete listener", "delete load balancer": "delete load balancer", "delete member": "delete member", "delete network": "delete network", "delete policy": "delete policy", "delete port forwarding": "delete port forwarding", "delete project": "delete project", "delete qos policy": "delete qos policy", "delete role": "delete role", "delete router": "delete router", "delete rule": "delete rule", "delete segments": "delete segments", "delete stack": "delete stack", "delete static route": "delete static route", "delete subnet": "delete subnet", "delete user": "delete user", "delete virtual adapter": "delete virtual adapter", "delete volume": "delete volume", "delete volume backup": "delete volume backup", "delete volume snapshot": "delete volume snapshot", "delete vpn": "delete vpn", "delete vpn IKE policy": "delete vpn IKE policy", "delete vpn IPsec policy": "delete vpn IPsec policy", "delete vpn endpoint groups": "delete vpn endpoint groups", "description": "description", "detach instance": "detach instance", "detach security group": "detach security group", "disable cinder service": "disable cinder service", "disable compute service": "disable compute service", "disable neutron agent": "disable neutron agent", "disassociate floating ip": "disassociate floating ip", "disconnect subnet": "disconnect subnet", "dns zones": "dns zones", "domain": "domain", "domains": "domains", "download image": "download image", "e.g. 2001:Db8::/48": "e.g. 2001:Db8::/48", "edit baremetal node": "edit baremetal node", "edit default pool": "edit default pool", "edit health monitor": "edit health monitor", "edit image": "edit image", "edit instance snapshot": "edit instance snapshot", "edit member": "edit member", "edit system permission": "edit system permission", "egress": "egress", "enable cinder service": "enable cinder service", "enable compute service": "enable compute service", "enable neutron agent": "enable neutron agent", "external port": "external port", "external ports": "external ports", "extra specs": "extra specs", "firewall": "firewall", "firewall policies": "firewall policies", "firewall rule": "firewall rule", "firewall rules": "firewall rules", "firewalls": "firewalls", "flavor": "flavor", "floating ip": "floating ip", "floating ips": "floating ips", "heat services": "heat services", "host aggregates": "host aggregates", "hosts": "hosts", "hypervisor": "hypervisor", "image": "image", "images": "images", "in": "in", "ingress": "ingress", "insert": "insert", "insert rule": "insert rule", "instance": "instance", "instance snapshot": "instance snapshot", "instance snapshots": "instance snapshots", "instance: {name}.": "instance: {name}.", "instances": "instances", "internal port": "internal port", "internal ports": "internal ports", "ipsec site connection": "ipsec site connection", "jump to the console": "jump to the console", "keypair": "keypair", "keypairs": "keypairs", "labels": "labels", "list page": "list page", "listener": "listener", "listeners": "listeners", "live migrate": "live migrate", "load balancer": "load balancer", "lock instance": "lock instance", "manage ports": "manage ports", "manage qos spec": "manage qos spec", "manage resource types": "manage resource types", "message": "message", "message.reason": "message.reason", "metadata": "metadata", "migrate": "migrate", "modify instance tags": "modify instance tags", "modify project tags": "modify project tags", "network": "network", "networks": "networks", "neutron agent": "neutron agent", "neutron agents": "neutron agents", "ns1.example.com admin.example.com 2013022001 86400 7200 604800 300 <ul><li>The primary name server for the domain, which is ns1.example.com or the first name server in the vanity name server list.</li><li>The responsible party for the domain: admin.example.com.</li><li>A timestamp that changes whenever you update your domain.</li><li>The number of seconds before the zone should be refreshed.</li><li>The number of seconds before a failed refresh should be retried.</li><li>The upper limit in seconds before a zone is considered no longer authoritative.</li><li>The negative result TTL (for example, how long a resolver should consider a negative result for a subdomain to be valid before retrying).</li></ul>": "ns1.example.com admin.example.com 2013022001 86400 7200 604800 300 <ul><li>The primary name server for the domain, which is ns1.example.com or the first name server in the vanity name server list.</li><li>The responsible party for the domain: admin.example.com.</li><li>A timestamp that changes whenever you update your domain.</li><li>The number of seconds before the zone should be refreshed.</li><li>The number of seconds before a failed refresh should be retried.</li><li>The upper limit in seconds before a zone is considered no longer authoritative.</li><li>The negative result TTL (for example, how long a resolver should consider a negative result for a subdomain to be valid before retrying).</li></ul>", "open external gateway": "open external gateway", "out": "out", "paste files to folder": "paste files to folder", "pause instance": "pause instance", "phone": "phone", "please select network": "please select network", "please select subnet": "please select subnet", "Please input the captcha!": "Please input the captcha!", "policy": "policy", "port": "port", "port forwarding": "port forwarding", "port forwardings": "port forwardings", "port groups": "port groups", "ports": "ports", "project": "project", "projects": "projects", "qemu_guest_agent enabled": "qemu_guest_agent enabled", "qoS policy": "qoS policy", "qos specs": "qos specs", "quota set to -1 means there is no quota limit on the current resource": "quota set to -1 means there is no quota limit on the current resource", "read": "read", "reboot instance": "reboot instance", "rebuild instance": "rebuild instance", "receive": "receive", "recordsets": "recordsets", "recover instance": "recover instance", "recycle bins": "recycle bins", "release fixed ip": "release fixed ip", "remove network": "remove network", "remove router": "remove router", "remove rule": "remove rule", "reserved_host": "reserved_host", "resize": "resize", "resume instance": "resume instance", "revert resize or migrate": "revert resize or migrate", "rh_priority": "rh_priority", "role": "role", "roles": "roles", "router": "router", "routers": "routers", "security group": "security group", "security group rules": "security group rules", "security groups": "security groups", "segments": "segments", "select an existing port": "select an existing port", "server group": "server group", "server groups": "server groups", "services": "services", "settings": "settings", "share": "share", "share access rules": "share access rules", "share group": "share group", "share group type": "share group type", "share groups": "share groups", "share instance": "share instance", "share instances": "share instances", "share metadata": "share metadata", "share network": "share network", "share server": "share server", "share servers": "share servers", "share type": "share type", "share types": "share types", "shelve instance": "shelve instance", "smtp.example.com": "smtp.example.com", "soft reboot instance": "soft reboot instance", "stack": "stack", "stack events": "stack events", "stack resources": "stack resources", "stacks": "stacks", "start instance": "start instance", "static routers": "static routers", "stop instance": "stop instance", "storage backend": "storage backend", "subnet": "subnet", "subnets": "subnets", "suspend instance": "suspend instance", "the Republic of Abkhazia": "the Republic of Abkhazia", "the folder is not empty": "the folder is not empty", "the policy is in use": "the policy is in use", "the router has connected subnet": "the router has connected subnet", "the vpn gateway is in use": "the vpn gateway is in use", "time / 24h": "time / 24h", "to delete": "to delete", "transmit": "transmit", "unlock instance": "unlock instance", "unpause instance": "unpause instance", "unshelve instance": "unshelve instance", "update": "update", "update status": "update status", "update template": "update template", "used": "used", "user": "user", "user group": "user group", "user groups": "user groups", "users": "users", "vCPUs": "vCPUs", "vCPUs and ram are not used for bare metal scheduling": "vCPUs and ram are not used for bare metal scheduling", "volume": "volume", "volume backup": "volume backup", "volume backups": "volume backups", "volume capacity": "volume capacity", "volume snapshot": "volume snapshot", "volume snapshots": "volume snapshots", "volume type": "volume type", "volume type qos": "volume type qos", "volume type {type}": "volume type {type}", "volume type {type} capacity": "volume type {type} capacity", "volume types": "volume types", "volumes": "volumes", "vpn IKE policy": "vpn IKE policy", "vpn IPsec policy": "vpn IPsec policy", "vpn endpoint groups": "vpn endpoint groups", "vpn services": "vpn services", "write": "write", "{ name } Format Error (e.g. *********** or ***********/24)": "{ name } Format Error (e.g. *********** or ***********/24)", "{ name } Format Error (e.g. FE80:0:0:0:0:0:0:1 or FE80:0:0:0:0:0:0:1/10)": "{ name } Format Error (e.g. FE80:0:0:0:0:0:0:1 or FE80:0:0:0:0:0:0:1/10)", "{ size } GiB": "{ size } GiB", "{ size } KiB": "{ size } KiB", "{ size } MiB": "{ size } MiB", "{ size } TiB": "{ size } TiB", "{ size } bytes": "{ size } bytes", "{action} successfully, instance: {name}.": "{action} successfully, instance: {name}.", "{action} successfully.": "{action} successfully.", "{action} {name} successfully.": "{action} {name} successfully.", "{hours} hours {leftMinutes} minutes {leftSeconds} seconds": "{hours} hours {leftMinutes} minutes {leftSeconds} seconds", "{interval, plural, =1 {one day} other {# days} } later delete": "{interval, plural, =1 {one day} other {# days} } later delete", "{interval, plural, =1 {one hour} other {# hours} } later delete": "{interval, plural, =1 {one hour} other {# hours} } later delete", "{interval, plural, =1 {one minute} other {# minutes} } later delete": "{interval, plural, =1 {one minute} other {# minutes} } later delete", "{interval, plural, =1 {one week} other {# weeks} } later delete": "{interval, plural, =1 {one week} other {# weeks} } later delete", "{minutes} minutes {leftSeconds} seconds": "{minutes} minutes {leftSeconds} seconds", "{name} type": "{name} type", "{name} type capacity": "{name} type capacity", "{name} type capacity (GiB)": "{name} type capacity (GiB)", "{name} type snapshots": "{name} type snapshots", "{name} {id} could not be found.": "{name} {id} could not be found.", "{number} {resource}": "{number} {resource}", "{pageSize} items/page": "{pageSize} items/page", "Network Rate": "Network Rate", "Network Packets Rate": "Network Packets Rate", "Block I/O Bytes Rate": "Block I/O Bytes Rate", "Block I/O Requests Rate": "Block I/O Requests Rate", "Home Page Title": "Home Page Title", "Maximum 30 characters allowed": "Maximum 30 characters allowed", "Login Page Title": "Login Page Title", "Browser Tab Title": "Browser Tab Title", "Login Page Logo": "Login <PERSON>", "Supported formats: PNG, JPG, JPEG. Max file size: 5MB": "Supported formats: PNG, JPG, JPEG. Max file size: 5MB", "Favicon": "Favicon", "Supported formats: ICO. Max file size: 1MB": "Supported formats: ICO. Max file size: 1MB", "Upload Image": "Upload Image", "Image uploaded successfully": "Image uploaded successfully", "Failed to upload image": "Failed to upload image", "Image must be smaller than {num}MB!": "Image must be smaller than {num}MB!", "Preview": "Preview", "Delete Login Logo": "Delete Login Logo", "Are you sure you want to delete the image?": "Are you sure you want to delete the image?", "Image deleted successfully": "Image deleted successfully", "Failed to delete image": "Failed to delete image", "You can only upload image files": "You can only upload image files", "You can only upload ICO files": "You can only upload ICO files", "Delete Favicon": "Delete Favicon", "Refresh": "Refresh", "{seconds} seconds": "{seconds} seconds"}