<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新健康巡检页面预览 - 5列布局</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f2f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background: white;
            padding: 24px;
            border-radius: 8px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .title {
            font-size: 18px;
            font-weight: 600;
            color: #262626;
            margin: 0 0 8px 0;
        }
        
        .subtitle {
            font-size: 14px;
            color: #8c8c8c;
        }
        

        
        .inspection-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
            padding: 24px;
        }

        .inspection-section h3 {
            margin: 0 0 16px 0;
            font-size: 16px;
            font-weight: 600;
            color: #262626;
        }

        .inspection-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 12px;
        }

        .inspection-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background: #fafafa;
            transition: all 0.3s ease;
            min-height: 48px;
            gap: 8px;
            font-size: 14px;
        }

        .inspection-item:hover {
            border-color: #1890ff;
            background: #f6ffed;
            box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
        }

        .inspection-item input[type="checkbox"] {
            margin-right: 8px;
        }

        /* 响应式设计 */
        @media (max-width: 1400px) {
            .inspection-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        @media (max-width: 1200px) {
            .inspection-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 768px) {
            .inspection-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 480px) {
            .inspection-grid {
                grid-template-columns: 1fr;
            }
        }

        .action-section {
            text-align: center;
            padding: 32px 0;
        }

        .start-button, .view-results-button {
            padding: 12px 32px;
            font-size: 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 0 8px;
            transition: all 0.3s ease;
        }

        .start-button {
            background: #1890ff;
            color: white;
        }

        .start-button:hover {
            background: #40a9ff;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }

        .view-results-button {
            background: #f0f0f0;
            color: #595959;
        }

        .view-results-button:hover {
            background: #d9d9d9;
            color: #262626;
        }
        
        .content {
            display: flex;
            gap: 24px;
            height: 600px;
        }
        
        .left-panel {
            width: 400px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .panel-header {
            padding: 16px 24px;
            border-bottom: 1px solid #f0f0f0;
            font-weight: 600;
            background: #fafafa;
        }
        
        .result-list {
            height: calc(100% - 57px);
            overflow-y: auto;
        }
        
        .result-item {
            padding: 16px 24px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .result-item:hover {
            background: #f5f5f5;
        }
        
        .result-item.selected {
            background: #e6f7ff;
            border-left: 3px solid #1890ff;
        }
        
        .result-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .status-icon {
            margin-right: 8px;
            font-size: 16px;
        }
        
        .result-name {
            font-weight: 600;
            color: #262626;
        }
        
        .result-category {
            font-size: 12px;
            color: #8c8c8c;
            margin-bottom: 4px;
        }
        
        .result-message {
            font-size: 13px;
            color: #595959;
            line-height: 1.4;
        }
        
        .right-panel {
            flex: 1;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 24px;
            overflow-y: auto;
        }
        
        .detail-section {
            margin-bottom: 24px;
        }

        .detail-table-simple {
            width: 100%;
            border-collapse: collapse;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .detail-table-simple th, .detail-table-simple td {
            padding: 16px 12px;
            border-bottom: 1px solid #f0f0f0;
            text-align: center;
            vertical-align: top;
        }

        .detail-table-simple th {
            background: #fafafa;
            font-weight: 600;
            color: #262626;
            border-bottom: 2px solid #f0f0f0;
            width: 33.33%;
        }

        .detail-column {
            vertical-align: top;
            padding: 16px 12px;
            border-bottom: 1px solid #f0f0f0;
            width: 33.33%;
        }

        .detail-value {
            color: #595959;
            line-height: 1.6;
            font-size: 14px;
            display: block;
            text-align: left;
        }

        .suggestion-content {
            color: #595959;
            line-height: 1.8;
            font-size: 14px;
            padding: 8px 0;
            white-space: pre-line;
        }
        
        .suggestion-section {
            background: #fff7e6;
            border: 1px solid #ffd591;
            border-radius: 8px;
            padding: 16px;
        }
        
        .suggestion-title {
            font-weight: 600;
            color: #d46b08;
            margin-bottom: 12px;
        }
        
        .suggestion-line {
            margin-bottom: 8px;
            padding-left: 16px;
            position: relative;
            line-height: 1.8;
        }
        
        .suggestion-line:before {
            content: '•';
            position: absolute;
            left: 0;
            color: #d46b08;
            font-weight: bold;
        }
        
        .error { color: #ff4d4f; }
        .warning { color: #faad14; }
        .normal { color: #52c41a; }
        
        .empty-state {
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #8c8c8c;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <div class="title">
                新健康巡检 - 5列布局展示
            </div>
            <div class="subtitle">
                配置平台级健康巡检工作项，确保平台各项健康指标正常运行
            </div>
        </div>



        <!-- 巡检项目配置 - 5列布局 -->
        <div class="inspection-section">
            <h3>HostOS资源检测</h3>
            <div class="inspection-grid">
                <div class="inspection-item">
                    <input type="checkbox" checked> HostOS分区利用率检测
                </div>
                <div class="inspection-item">
                    <input type="checkbox" checked> HostOS CPU利用率检测
                </div>
                <div class="inspection-item">
                    <input type="checkbox" checked> HostOS内存利用率检测
                </div>
                <div class="inspection-item">
                    <input type="checkbox" checked> HostOS告警事件检测
                </div>
                <div class="inspection-item">
                    <input type="checkbox" checked> 物理网卡连通状态检测
                </div>
            </div>
        </div>

        <div class="inspection-section">
            <h3>云平台资源检测 (展示5列布局)</h3>
            <div class="inspection-grid">
                <div class="inspection-item">
                    <input type="checkbox" checked> 平台数据库状态检测
                </div>
                <div class="inspection-item">
                    <input type="checkbox" checked> CinderVolume服务
                </div>
                <div class="inspection-item">
                    <input type="checkbox" checked> NovaCompute服务
                </div>
                <div class="inspection-item">
                    <input type="checkbox" checked> NeutronOpenvswitch服务
                </div>
                <div class="inspection-item">
                    <input type="checkbox" checked> Keepalived服务
                </div>
                <div class="inspection-item">
                    <input type="checkbox" checked> NeutronDhcp服务
                </div>
                <div class="inspection-item">
                    <input type="checkbox" checked> Neutron服务
                </div>
                <div class="inspection-item">
                    <input type="checkbox" checked> Glance服务
                </div>
                <div class="inspection-item">
                    <input type="checkbox" checked> NeutronServer服务
                </div>
                <div class="inspection-item">
                    <input type="checkbox" checked> Nova服务
                </div>
                <div class="inspection-item">
                    <input type="checkbox" checked> Cinder服务
                </div>
                <div class="inspection-item">
                    <input type="checkbox" checked> 平台HA状态检测
                </div>
            </div>
        </div>

        <!-- 开始巡检按钮 -->
        <div class="action-section">
            <button class="start-button">开始巡检</button>
            <button class="view-results-button">查看巡检结果</button>
        </div>
    </div>
</body>
</html>
