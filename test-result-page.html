<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>巡检结果页面预览</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f2f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background: white;
            padding: 24px;
            border-radius: 8px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .title {
            font-size: 18px;
            font-weight: 600;
            color: #262626;
            margin: 0 0 8px 0;
        }
        
        .subtitle {
            font-size: 14px;
            color: #8c8c8c;
        }
        

        
        .tabs {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
            padding: 0 24px;
        }
        
        .tab {
            display: inline-block;
            padding: 16px 20px;
            font-size: 14px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
        }
        
        .tab.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
        }
        
        .content {
            display: flex;
            gap: 24px;
            height: 600px;
        }
        
        .left-panel {
            width: 400px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .panel-header {
            padding: 16px 24px;
            border-bottom: 1px solid #f0f0f0;
            font-weight: 600;
            background: #fafafa;
        }
        
        .result-list {
            height: calc(100% - 57px);
            overflow-y: auto;
        }
        
        .result-item {
            padding: 16px 24px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .result-item:hover {
            background: #f5f5f5;
        }
        
        .result-item.selected {
            background: #e6f7ff;
            border-left: 3px solid #1890ff;
        }
        
        .result-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .status-icon {
            margin-right: 8px;
            font-size: 16px;
        }
        
        .result-name {
            font-weight: 600;
            color: #262626;
        }
        
        .result-category {
            font-size: 12px;
            color: #8c8c8c;
            margin-bottom: 4px;
        }
        
        .result-message {
            font-size: 13px;
            color: #595959;
            line-height: 1.4;
        }
        
        .right-panel {
            flex: 1;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 24px;
            overflow-y: auto;
        }
        
        .detail-section {
            margin-bottom: 24px;
        }
        
        .detail-item {
            display: flex;
            margin-bottom: 12px;
            line-height: 1.6;
        }
        
        .detail-label {
            font-weight: 600;
            color: #262626;
            min-width: 80px;
            margin-right: 12px;
        }
        
        .detail-value {
            flex: 1;
            color: #595959;
        }
        
        .suggestion-section {
            background: #fff7e6;
            border: 1px solid #ffd591;
            border-radius: 8px;
            padding: 16px;
        }
        
        .suggestion-title {
            font-weight: 600;
            color: #d46b08;
            margin-bottom: 12px;
        }
        
        .suggestion-line {
            margin-bottom: 8px;
            padding-left: 16px;
            position: relative;
            line-height: 1.8;
        }
        
        .suggestion-line:before {
            content: '•';
            position: absolute;
            left: 0;
            color: #d46b08;
            font-weight: bold;
        }
        
        .error { color: #ff4d4f; }
        .warning { color: #faad14; }
        .normal { color: #52c41a; }
        
        .empty-state {
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #8c8c8c;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <div class="title">
                检测结果：共32项，其中28项正常，2项警告，2项异常
            </div>
            <div class="subtitle">
                检测时间：2024-07-22 16:30:45 - 2024-07-22 16:31:30 耗时：00:00:45
            </div>
        </div>



        <!-- 标签页 -->
        <div class="tabs">
            <div class="tab active">
                <span style="color: #1890ff; margin-right: 8px;">📄</span>全部(32)
            </div>
            <div class="tab">
                <span style="color: #52c41a; margin-right: 8px;">✓</span>正常(28)
            </div>
            <div class="tab">
                <span style="color: #faad14; margin-right: 8px;">⚠</span>警告(2)
            </div>
            <div class="tab">
                <span style="color: #ff4d4f; margin-right: 8px;">✕</span>异常(2)
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="content">
            <div class="left-panel">
                <div class="panel-header">检测结果</div>
                <div class="result-list">
                    <div class="result-item selected">
                        <div class="result-header">
                            <span class="status-icon error">●</span>
                            <span class="result-name">集群状态</span>
                        </div>
                        <div class="result-category">存储资源巡检</div>
                        <div class="result-message">UnknownHostException: null</div>
                    </div>
                    <div class="result-item">
                        <div class="result-header">
                            <span class="status-icon error">●</span>
                            <span class="result-name">存储状态安全检查</span>
                        </div>
                        <div class="result-category">存储资源巡检</div>
                        <div class="result-message">Connection timeout</div>
                    </div>
                    <div class="result-item">
                        <div class="result-header">
                            <span class="status-icon warning">●</span>
                            <span class="result-name">CPU利用率检测</span>
                        </div>
                        <div class="result-category">HostOS资源检测</div>
                        <div class="result-message">CPU usage is 85%</div>
                    </div>
                    <div class="result-item">
                        <div class="result-header">
                            <span class="status-icon warning">●</span>
                            <span class="result-name">内存利用率检测</span>
                        </div>
                        <div class="result-category">HostOS资源检测</div>
                        <div class="result-message">Memory usage is 78%</div>
                    </div>
                </div>
            </div>

            <div class="right-panel">
                <div class="detail-section">
                    <h3>检测结果详情</h3>
                    <div class="detail-item">
                        <span class="detail-label">检测项：</span>
                        <span class="detail-value">集群状态</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">分类：</span>
                        <span class="detail-value">存储资源巡检</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">状态：</span>
                        <span class="detail-value error">异常</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">检测结果：</span>
                        <span class="detail-value">UnknownHostException: null</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">详细说明：</span>
                        <span class="detail-value">集群连接异常，请检查网络连接状态。</span>
                    </div>
                </div>

                <div class="suggestion-section">
                    <div class="suggestion-title">修复建议</div>
                    <div class="suggestion-line">检查网络连接是否正常</div>
                    <div class="suggestion-line">确认集群服务是否启动</div>
                    <div class="suggestion-line">验证防火墙设置</div>
                    <div class="suggestion-line">检查DNS解析是否正确</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
